{"name": "ruoyi-vue-plus", "version": "4.8.3", "description": "RuoYi-Vue-Plus后台管理系统", "author": "LionLi", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:test": "vite build --mode development", "preview": "vite preview", "deploy": "vite build --mode development && node ./deploy.js"}, "repository": {"type": "git", "url": "https://gitlab.zsnetwork.com/scaffold/frontend/zs-admin.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "9.5.0", "axios": "1.11.0", "crypto-js": "4.2.0", "echarts": "5.4.0", "element-plus": "2.10.7", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "nprogress": "0.2.0", "pinia": "2.0.22", "sm-crypto": "^0.3.13", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "sass": "1.56.1", "ssh2": "^1.14.0", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "unplugin-vue-setup-extend-plus": "0.4.9", "vite-plugin-zip-pack": "1.2.4"}}