import request from "@/utils/request";

// 查询字典数据列表
export function listData(query, stype = 1) {
  const apiConfig = {
    1: "/system/dict/data/list",
    2: "/interfaceDict/itemPage",
  };
  return request({
    url: apiConfig[stype],
    method: "get",
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode, stype = 1) {
  const apiConfig = {
    1: "/system/dict/data/" + dictCode,
    2: "/interfaceDict/getItem/" + dictCode,
  };
  return request({
    url: apiConfig[stype],
    method: "get",
  });
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: "/system/dict/data/type/" + dictType,
    method: "get",
  });
}

// 新增字典数据
export function addData(data, stype = 1) {
  const apiConfig = {
    1: "/system/dict/data",
    2: "/interfaceDict/addItem",
  };
  return request({
    url: apiConfig[stype],
    method: "post",
    data: data,
  });
}

// 修改字典数据
export function updateData(data, stype = 1) {
  const apiConfig = {
    1: "/system/dict/data",
    2: "/interfaceDict/updateItem",
  };
  return request({
    url: apiConfig[stype],
    method: "put",
    data: data,
  });
}

// 删除字典数据
export function delData(dictCode, stype = 1) {
  const apiConfig = {
    1: "/system/dict/data/" + dictCode,
    2: `/interfaceDict/delItems/${dictCode}`,
  };
  return request({
    url: apiConfig[stype],
    method: "delete",
  });
}

// 查询字典数据列表
export function itemListOptions() {
  return request({
    url: "/interfaceDict/itemList",
    method: "get",
  });
}
