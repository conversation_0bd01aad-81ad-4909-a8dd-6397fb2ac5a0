import request from "@/utils/request";
// 查询字典类型列表
export function listType(query, stype = 1) {
  const apiConfig = {
    1: "/system/dict/type/list",
    2: "/interfaceDict/typePage",
  };
  return request({
    url: apiConfig[stype],
    method: "get",
    params: query,
  });
}

// 查询字典类型详细
export function getType(dictId, stype = 1) {
  const apiConfig = {
    1: "/system/dict/type/" + dictId,
    2: "/interfaceDict/getType/" + dictId,
  };
  return request({
    url: apiConfig[stype],
    method: "get",
  });
}

// 新增字典类型
export function addType(data, stype = 1) {
  const apiConfig = {
    1: "/system/dict/type",
    2: "/interfaceDict/addType",
  };
  return request({
    url: apiConfig[stype],
    method: "post",
    data: data,
  });
}

// 修改字典类型
export function updateType(data, stype = 1) {
  const apiConfig = {
    1: "/system/dict/type",
    2: "/interfaceDict/updateType",
  };
  return request({
    url: apiConfig[stype],
    method: "put",
    data: data,
  });
}

// 删除字典类型
export function delType(dictId, stype = 1) {
  const apiConfig = {
    1: "/system/dict/type/" + dictId,
    2: `/interfaceDict/delTypes/${dictId}`,
  };
  return request({
    url: apiConfig[stype],
    method: "delete",
  });
}

// 刷新字典缓存
export function refreshCache(stype = 1) {
  const apiConfig = {
    1: "/system/dict/type/refreshCache",
    2: "/interfaceDict/refreshDictCache",
  };
  return request({
    url: apiConfig[stype],
    method: "delete",
  });
}

// 获取字典选择框列表
export function optionselect(stype = 1) {
  return request({
    url: "/system/dict/type/optionselect",
    method: "get",
  });
}

// 获取字典选择框列表
export function typeListOptions() {
  return request({
    url: "/interfaceDict/typeList",
    method: "get",
  });
}
