import request from "@/utils/request";

// ==================== API分类管理 ====================

// 获取API分类树
export function getApiCategoryTree() {
  return request({
    url: "interfaceCate/selectTree",
    method: "get",
  });
}

// 新增API分类
export function addApiCategory(data) {
  return request({
    url: "/interfaceCate/add",
    method: "post",
    data,
  });
}

// 修改API分类
export function updateApiCategory(data) {
  return request({
    url: "/interfaceCate/update",
    method: "post",
    data,
  });
}

// 删除API分类
export function deleteApiCategory(id) {
  return request({
    url: `/interfaceCate/delete?id=${id}`,
    method: "post",
  });
}

// ==================== API接口管理 ====================

// 获取API接口列表
export function getApiInterfaceList(params) {
  return request({
    url: "/interfaceConfig/selectPage",
    method: "get",
    params,
  });
}

// 获取API接口详情
export function getApiInterface(id) {
  return request({
    url: `/interfaceConfig/selectOne?id=${id}`,
    method: "get",
  });
}

// 新增API接口 - 基本信息
export function addApiInterfaceBasic(data) {
  return request({
    url: "/interfaceConfig/addInterfaceConfig",
    method: "post",
    data,
  });
}

// 更新API接口 - 基本信息
export function updateApiInterfaceBasic(data) {
  return request({
    url: "/interfaceConfig/updateInterfaceConfig",
    method: "post",
    data,
  });
}

// 新增API接口 - 权限配置
export function addApiInterfaceAuth(data) {
  return request({
    url: "/interfaceConfig/addTokenConfig",
    method: "post",
    data,
  });
}

// 更新API接口 - 权限配置
export function updateApiInterfaceAuth(data) {
  return request({
    url: "/interfaceConfig/updateTokenConfig",
    method: "post",
    data,
  });
}

// 新增API接口 - 数据源配置
export function addApiInterfaceDataSource(data) {
  return request({
    url: "/interfaceConfig/addInterfaceTokenConfig",
    method: "post",
    data,
  });
}

// 更新API接口 - 数据源配置
export function updateApiInterfaceDataSource(data) {
  return request({
    url: "/interfaceConfig/updateInterfaceTokenConfig",
    method: "post",
    data,
  });
}

// 删除API接口
export function deleteApiInterface(params) {
  return request({
    url: "/interfaceConfig/delete",
    method: "post",
    params,
  });
}
