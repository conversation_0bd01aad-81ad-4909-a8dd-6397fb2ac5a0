import request from "@/utils/request";

// 获取搜索树形数据
export function getSearchTreeData() {
  return request({
    url: "interfaceCate/selectTree",
    method: "get",
  });
}
/**
 * 查询入参配置
 * @param { interfaceId } params
 * @returns
 */
export function getSearchFilterConfig(params) {
  return request({
    url: "/interfaceParam/paramTree",
    method: "get",
    params,
  });
}

// 新增/更新入参配置
export function saveOrUpdateParams(data) {
  return request({
    url: "/interfaceParam/saveOrUpdateParams",
    method: "post",
    headers: {
      needSign: false,
    },
    data,
  });
}

// 删除入参配置
export function deleteSearchFilterConfig(params) {
  return request({
    url: "/interfaceParam/deleteParam",
    method: "post",
    params,
  });
}

// 查询出参配置
export function getSearchOutputConfig(params) {
  return request({
    url: "/interfaceParam/responseMappingTree",
    method: "get",
    params,
  });
}

// 新增、更新出参配置
export function saveOrUpdateResponseMappings(data) {
  return request({
    url: "/interfaceParam/saveOrUpdateResponseMappings",
    method: "post",
    data,
  });
}

// 删除出参配置
export function deleteSearchOutputConfig(params) {
  return request({
    url: "/interfaceParam/deleteResponseMapping",
    method: "post",
    params,
  });
}

// 查询请求头配置
export function getSearchHeaderConfig(params) {
  return request({
    url: "/interfaceParam/headerList",
    method: "get",
    params,
  });
}

// 新增请求头配置
export function addSearchHeaderConfig(data) {
  return request({
    url: "/interfaceParam/addHeader",
    method: "post",
    data,
  });
}

// 更新请求头配置
export function updateSearchHeaderConfig(data) {
  return request({
    url: "/interfaceParam/updateHeader",
    method: "post",
    data,
  });
}

// 删除请求头配置
export function deleteSearchHeaderConfig(params) {
  return request({
    url: "/interfaceParam/deleteHeader",
    method: "post",
    params,
  });
}

// 查询出参配置
export function getSearchResponseConfig(params) {
  return request({
    url: "/interfaceParam/queryResponseConfig",
    method: "get",
    params,
  });
}

// 新增/更新出参配置
export function saveOrUpdateSearchResponseConfig(data) {
  return request({
    url: "/interfaceParam/saveOrUpdateResponseConfig",
    method: "post",
    data,
  });
}

export function updateApiInterfaceStatus(data) {
  return request({
    url: "/interfaceConfig/updateStatus",
    method: "post",
    data,
  });
}

// 关联字典到参数
export function associateDict(data) {
  return request({
    url: "/interfaceDict/saveRela",
    method: "post",
    data,
  });
}

// 取消字典关联
export function disassociateDict(data) {
  return request({
    url: "/interfaceDict/cancelRela",
    method: "post",
    data,
  });
}
