import request from "@/utils/request";
/**
 * http://192.168.116.8:3000/project/263/interface/api/35846
 * 数据库分页查询接口列表
 * @param {  } params
 * @returns
 */
export function getDatabBasePageList(params) {
  return request({
    url: "/dataBaseInfo/selectPage",
    method: "get",
    params,
  });
}

/**
 * http://192.168.116.8:3000/project/263/interface/api/35853
 * 获取数据库驱动类名
 * @param { databaseType } params
 * @returns
 */
export function getDatabBaseDriverClassName(params) {
  return request({
    url: "/dataBaseInfo/getDriverClassName",
    method: "get",
    params,
  });
}

/**
 * http://192.168.116.8:3000/project/263/interface/api/35854
 * 测试数据库连接
 * @param { databaseId, databaseCode, databaseName, databaseType, username, password,driverClassName, jdbcUrl } data
 * @returns
 */
export function verifyDatabaseConnection(data) {
  return request({
    url: "/dataBaseInfo/verifyConnection",
    method: "post",
    data,
  });
}

/**
 * http://192.168.116.8:3000/project/263/interface/api/35867
 * 更新数据库状态
 * @param { databaseId, status } data
 * @returns
 */
export function updateDatabaseStatus(data) {
  return request({
    url: "/dataBaseInfo/updateStatus",
    method: "post",
    data,
  });
}

/**
 * http://192.168.116.8:3000/project/263/interface/api/35874
 * 新增数据库
 * @param { databaseCode, databaseName, databaseType, username, password,driverClassName, jdbcUrl } data
 * @returns
 */
export function addDatabase(data) {
  return request({
    url: "/dataBaseInfo/add",
    method: "post",
    data,
  });
}

/**
 * http://192.168.116.8:3000/project/263/interface/api/35881
 * 更新数据库
 * @param { databaseId, databaseCode, databaseName, databaseType, username, password,driverClassName, jdbcUrl } data
 * @returns
 */
export function updateDatabase(data) {
  return request({
    url: "/dataBaseInfo/update",
    method: "post",
    data,
  });
}

/**
 * http://192.168.116.8:3000/project/263/interface/api/35888
 * 删除数据库
 * @param { databaseId } params
 * @returns
 */
export function deleteDatabase(params) {
  return request({
    url: "/dataBaseInfo/delete",
    method: "post",
    params,
  });
}
