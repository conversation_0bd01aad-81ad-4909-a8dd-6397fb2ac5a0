import request from "@/utils/request";

// 获取搜索树形数据
export function getSearchTreeData() {
  return request({
    url: "interfaceCate/selectTree",
    method: "get",
  });
}

// 获取搜索筛选配置
export function getSearchFilterConfig(params) {
  return request({
    url: "/interfaceQuery/queryParam",
    method: "get",
    params,
  });
}

// 获取搜索表格列配置
export function getSearchTableColumns(params) {
  return request({
    url: "/interfaceQuery/queryResponse",
    method: "get",
    params,
  });
}

// 获取搜索表格数据
export function getSearchTableData(data) {
  return request({
    url: "/interfaceQuery/queryData",
    method: "post",
    data,
  });
}

// 获取详情数据
export function getSearchDetail(params) {
  return request({
    url: `/interfaceQuery/queryBatchDetailPage`,
    method: "get",
    params,
  });
}

// 批量操作
export function batchOperation(data) {
  return request({
    url: "/interfaceQuery/executeQueryBatch",
    method: "post",
    data,
  });
}

// 查询批量查询记录
export function getSearchBatchList(params) {
  return request({
    url: "/interfaceQuery/queryBatchPage",
    method: "get",
    params,
  });
}

// 导出批量查询记录详情
export function exportBatchDetail(params) {
  return request({
    url: "/interfaceQuery/exportBatchDetail",
    method: "get",
    params,
  });
}
