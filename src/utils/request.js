import axios from "axios";
import {
  ElNotification,
  ElMessageBox,
  ElMessage,
  ElLoading,
} from "element-plus";
import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import { tansParams, blobValidate } from "@/utils/ruoyi";
import cache from "@/plugins/cache";
import { saveAs } from "file-saver";
import useUserStore from "@/store/modules/user";
import { SignUtils } from "@/utils/signUtils";

let downloadLoadingInstance;
// 是否显示重新登录
export let isRelogin = { show: false };

// 签名配置
const signConfig = {
  accessId: import.meta.env.VITE_APP_ACCESS_ID,
  accessSecret: import.meta.env.VITE_APP_ACCESS_SECRET,
};

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// 对应国际化资源文件后缀
axios.defaults.headers["Content-Language"] = "zh_CN";
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 10000,
});

/**
 * 常量定义
 */
export const HttpHeaderConstants = {
  ZS_ACCESS_ID: "Zs-Access-Id",
  ZS_TIMESTAMP: "Zs-Timestamp",
  ZS_NONCE: "Zs-Nonce",
  ZS_SIGNATURE: "Zs-Signature",
};

function parseQueryString(queryString) {
  return queryString
    .trim()
    .split("&")
    .reduce((acc, param) => {
      if (!param) return acc;
      const [key, value] = param.split("=").map(decodeURIComponent);
      acc[key] = value;
      return acc;
    }, {});
}

/**
 * 移除对象中的null和undefined属性
 * @param {Object} obj - 要处理的对象
 * @returns {Object} 过滤后的对象
 */
function removeNullAndUndefinedProperties(obj) {
  if (obj === null || obj === undefined) {
    return undefined;
  }
  if (Array.isArray(obj)) {
    return obj
      .map((item) => removeNullAndUndefinedProperties(item))
      .filter((item) => item !== undefined);
  }
  if (typeof obj === "object" && obj !== null) {
    const result = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = removeNullAndUndefinedProperties(obj[key]);
        if (value !== undefined) {
          result[key] = value;
        }
      }
    }
    return result;
  }
  return obj !== null && obj !== undefined ? obj : undefined;
}

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    // 是否需要签名验证
    const needSign = (config.headers || {}).needSign !== false;

    if (getToken() && !isToken) {
      config.headers["Authorization"] = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }

    // 添加签名验证逻辑
    if (needSign && signConfig.accessId && signConfig.accessSecret) {
      try {
        // 解析URL中的查询参数
        const parseUrlParams = (url) => {
          const params = {};
          const queryString = url.split("?")[1];
          if (queryString) {
            queryString.split("&").forEach((pair) => {
              const [key, value] = pair.split("=");
              if (key)
                params[decodeURIComponent(key)] = decodeURIComponent(
                  value || ""
                );
            });
          }
          return params;
        };

        // 提取URL中的查询参数
        const urlQueryParams = parseUrlParams(config.url);

        // 提取config.params中的参数并合并
        const urlParams = { ...urlQueryParams };
        if (config.params) {
          Object.assign(urlParams, parseQueryString(tansParams(config.params)));
        }

        // 提取请求体参数
        const bodyParams = {};
        if (config.data && typeof config.data === "object") {
          Object.assign(
            bodyParams,
            removeNullAndUndefinedProperties(config.data)
          );
        }

        // 生成签名请求头
        const signedHeaders = SignUtils.createSignedHeaders(
          signConfig.accessId,
          signConfig.accessSecret,
          urlParams,
          bodyParams
        );

        // 合并签名请求头
        Object.assign(config.headers, signedHeaders);

        console.log("已添加签名验证:", {
          url: config.url,
          method: config.method,
          signedHeaders,
          urlParams,
          bodyParams,
        });
      } catch (error) {
        console.error("签名生成失败:", error);
      }
    }

    // get请求映射params参数
    if (config.method === "get" && config.params) {
      // 过滤null和undefined参数
      const filteredParams = removeNullAndUndefinedProperties(config.params);
      let url = config.url + "?" + tansParams(filteredParams);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    if (
      !isRepeatSubmit &&
      (config.method === "post" || config.method === "put")
    ) {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === "object"
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      };
      const sessionObj = cache.session.getJSON("sessionObj");
      if (
        sessionObj === undefined ||
        sessionObj === null ||
        sessionObj === ""
      ) {
        cache.session.setJSON("sessionObj", requestObj);
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_data = sessionObj.data; // 请求数据
        const s_time = sessionObj.time; // 请求时间
        const interval = 10; // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = "数据正在处理，请勿重复提交";
          console.warn(`[${s_url}]: ` + message);
          return Promise.reject(new Error(message));
        } else {
          cache.session.setJSON("sessionObj", requestObj);
        }
      }
    }
    return config;
  },
  (error) => {
    console.log(error);
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode["default"];
    // 二进制数据则直接返回
    if (
      res.request.responseType === "blob" ||
      res.request.responseType === "arraybuffer"
    ) {
      return res.data;
    }
    if (code === 401) {
      if (!isRelogin.show) {
        isRelogin.show = true;
        ElMessageBox.confirm(
          "登录状态已过期，您可以继续留在该页面，或者重新登录",
          "系统提示",
          {
            confirmButtonText: "重新登录",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            isRelogin.show = false;
            useUserStore()
              .logOut()
              .then(() => {
                location.href =
                  import.meta.env.VITE_APP_CONTEXT_PATH + "/index";
              });
          })
          .catch(() => {
            isRelogin.show = false;
          });
      }
      return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
    } else if (code === 500) {
      ElMessage({ message: msg, type: "error" });
      return Promise.reject(new Error(msg));
    } else if (code === 601) {
      ElMessage({ message: msg, type: "warning" });
      return Promise.reject(new Error(msg));
    } else if (code !== 200) {
      ElNotification.error({ title: msg });
      return Promise.reject("error");
    } else {
      return Promise.resolve(res.data);
    }
  },
  (error) => {
    console.log("err" + error);
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }
    ElMessage({ message: message, type: "error", duration: 5 * 1000 });
    return Promise.reject(error);
  }
);

// 通用下载方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: "正在下载数据，请稍候",
    background: "rgba(0, 0, 0, 0.7)",
  });
  return service
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params);
        },
      ],
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      responseType: "blob",
      ...config,
    })
    .then(async (data) => {
      const isBlob = blobValidate(data);
      if (isBlob) {
        const blob = new Blob([data]);
        saveAs(blob, filename);
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
        ElMessage.error(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r);
      ElMessage.error("下载文件出现错误，请联系管理员！");
      downloadLoadingInstance.close();
    });
}

// 创建带签名的请求方法
export const signedRequest = {
  get: (url, params = {}, config = {}) => {
    return service.get(url, {
      params,
      ...config,
      headers: {
        needSign: true,
        ...config.headers,
      },
    });
  },

  post: (url, data = {}, config = {}) => {
    return service.post(url, data, {
      ...config,
      headers: {
        needSign: true,
        ...config.headers,
      },
    });
  },

  put: (url, data = {}, config = {}) => {
    return service.put(url, data, {
      ...config,
      headers: {
        needSign: true,
        ...config.headers,
      },
    });
  },

  delete: (url, config = {}) => {
    return service.delete(url, {
      ...config,
      headers: {
        needSign: true,
        ...config.headers,
      },
    });
  },
};

// 创建不需要签名的请求方法
export const unsignedRequest = {
  get: (url, params = {}, config = {}) => {
    return service.get(url, {
      params,
      ...config,
      headers: {
        needSign: false,
        ...config.headers,
      },
    });
  },

  post: (url, data = {}, config = {}) => {
    return service.post(url, data, {
      ...config,
      headers: {
        needSign: false,
        ...config.headers,
      },
    });
  },

  put: (url, data = {}, config = {}) => {
    return service.put(url, data, {
      ...config,
      headers: {
        needSign: false,
        ...config.headers,
      },
    });
  },

  delete: (url, config = {}) => {
    return service.delete(url, {
      ...config,
      headers: {
        needSign: false,
        ...config.headers,
      },
    });
  },
};

export default service;
