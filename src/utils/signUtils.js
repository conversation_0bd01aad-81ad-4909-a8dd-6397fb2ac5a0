import CryptoJS from "crypto-js";

/**
 * 签名工具类
 */
export class SignUtils {
  static HEADERS = {
    ZS_ACCESS_ID: "Zs-Access-Id",
    ZS_TIMESTAMP: "Zs-Timestamp",
    ZS_NONCE: "Zs-Nonce",
    ZS_SIGNATURE: "Zs-Signature",
  };

  /**
   * 使用 HMAC-SHA256 算法生成签名
   * @param {string} secret 密钥
   * @param {string} content 待签名内容
   * @returns {string} Base64编码的签名
   */
  static hmacSHA256(secret, content) {
    try {
      const hmac = CryptoJS.HmacSHA256(content, secret);
      return CryptoJS.enc.Base64.stringify(hmac);
    } catch (error) {
      console.error("hmacSHA256 error:", error);
      return "";
    }
  }

  /**
   * 生成随机字符串
   * @param {number} length 字符串长度
   * @returns {string} 随机字符串
   */
  static generateNonce(length = 16) {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 获取当前时间戳
   * @returns {string} 毫秒级时间戳字符串
   */
  static getCurrentTimestamp() {
    return Date.now().toString();
  }

  /**
   * 将对象转换为排序后的查询字符串
   * @param {Object} params 参数对象
   * @returns {string} 排序后的查询字符串
   */
  static sortJoinParams(params) {
    const sortedKeys = Object.keys(params).sort();
    return sortedKeys
      .map((key) => {
        let value = params[key];
        if (
          Array.isArray(value) ||
          (typeof value === "object" && value !== null)
        ) {
          value = JSON.stringify(value);
        }
        return `${key}=${value}`;
      })
      .join("&");
  }

  /**
   * 构建请求参数字符串
   * @param {Object} headers 请求头
   * @param {Object} params URL参数
   * @param {Object} body 请求体
   * @returns {string} 排序后的参数字符串
   */
  static buildRequestParams(headers, params = {}, body = {}) {
    const allParams = {};

    // 添加签名相关的请求头
    const signHeaders = [
      this.HEADERS.ZS_ACCESS_ID,
      this.HEADERS.ZS_TIMESTAMP,
      this.HEADERS.ZS_NONCE,
    ];

    signHeaders.forEach((headerName) => {
      if (headers[headerName]) {
        allParams[headerName] = headers[headerName];
      }
    });

    // 添加URL参数
    Object.assign(allParams, params);

    // 添加请求体参数
    Object.assign(allParams, body);

    console.log("排序拼成后的参数", this.sortJoinParams(allParams));

    return this.sortJoinParams(allParams);
  }

  /**
   * 生成签名
   * @param {string} accessSecret 访问密钥
   * @param {string} requestParams 请求参数字符串
   * @returns {string} 签名
   */
  static generateSignature(accessSecret, requestParams) {
    return this.hmacSHA256(accessSecret, requestParams);
  }

  /**
   * 创建签名请求头
   * @param {string} accessId 访问ID
   * @param {string} accessSecret 访问密钥
   * @param {Object} params URL参数
   * @param {Object} body 请求体
   * @returns {Object} 包含签名的请求头
   */
  static createSignedHeaders(accessId, accessSecret, params = {}, body = {}) {
    const timestamp = this.getCurrentTimestamp();
    const nonce = this.generateNonce();

    const headers = {
      [this.HEADERS.ZS_ACCESS_ID]: accessId,
      [this.HEADERS.ZS_TIMESTAMP]: timestamp,
      [this.HEADERS.ZS_NONCE]: nonce,
    };
    console.log("加密前的参数", { params, body });

    // 构建请求参数
    const requestParams = this.buildRequestParams(headers, params, body);

    // 生成签名
    const signature = this.generateSignature(accessSecret, requestParams);
    // 添加签名到请求头
    headers[this.HEADERS.ZS_SIGNATURE] = signature;

    return headers;
  }

  /**
   * 验证时间戳是否在有效期内（前后5分钟）
   * @param {string} timestamp 时间戳
   * @param {number} toleranceMinutes 容差分钟数，默认5分钟
   * @returns {boolean} 是否有效
   */
  static isValidTimestamp(timestamp, toleranceMinutes = 5) {
    const currentTime = Date.now();
    const requestTime = parseInt(timestamp);
    const toleranceMs = toleranceMinutes * 60 * 1000;

    return Math.abs(currentTime - requestTime) <= toleranceMs;
  }
}

/**
 * 带签名的HTTP请求工具类
 */
export class SignedHttpClient {
  constructor(config) {
    this.config = config;
  }

  /**
   * 发送带签名的HTTP请求
   * @param {Object} config 请求配置
   * @returns {Promise}
   */
  async request(config) {
    const {
      url,
      method = "GET",
      params = {},
      body = {},
      headers = {},
    } = config;

    // 创建签名请求头
    const signedHeaders = SignUtils.createSignedHeaders(
      this.config.accessId,
      this.config.accessSecret,
      params,
      body
    );

    // 合并请求头
    const finalHeaders = {
      "Content-Type": "application/json",
      ...headers,
      ...signedHeaders,
    };

    // 构建请求选项
    const requestOptions = {
      method,
      headers: finalHeaders,
    };

    // 添加请求体
    if (method !== "GET" && Object.keys(body).length > 0) {
      requestOptions.body = JSON.stringify(body);
    }

    // 构建URL（添加查询参数）
    let finalUrl = url;
    if (Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value));
      });
      finalUrl += `?${searchParams.toString()}`;
    }

    try {
      const response = await fetch(finalUrl, requestOptions);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Signed request failed:", error);
      throw error;
    }
  }

  /**
   * GET请求
   * @param {string} url 请求URL
   * @param {Object} params URL参数
   * @param {Object} headers 请求头
   * @returns {Promise}
   */
  async get(url, params, headers) {
    return this.request({ url, method: "GET", params, headers });
  }

  /**
   * POST请求
   * @param {string} url 请求URL
   * @param {Object} body 请求体
   * @param {Object} headers 请求头
   * @returns {Promise}
   */
  async post(url, body, headers) {
    return this.request({ url, method: "POST", body, headers });
  }

  /**
   * PUT请求
   * @param {string} url 请求URL
   * @param {Object} body 请求体
   * @param {Object} headers 请求头
   * @returns {Promise}
   */
  async put(url, body, headers) {
    return this.request({ url, method: "PUT", body, headers });
  }

  /**
   * DELETE请求
   * @param {string} url 请求URL
   * @param {Object} headers 请求头
   * @returns {Promise}
   */
  async delete(url, headers) {
    return this.request({ url, method: "DELETE", headers });
  }
}

/**
 * Vue3 Composition API 签名工具
 */
export function useSignUtils() {
  /**
   * 创建签名客户端
   * @param {Object} config 配置对象
   * @returns {SignedHttpClient}
   */
  const createSignedClient = (config) => {
    return new SignedHttpClient(config);
  };

  /**
   * 生成签名请求头
   * @param {string} accessId 访问ID
   * @param {string} accessSecret 访问密钥
   * @param {Object} params URL参数
   * @param {Object} body 请求体
   * @returns {Object}
   */
  const generateSignedHeaders = (
    accessId,
    accessSecret,
    params = {},
    body = {}
  ) => {
    return SignUtils.createSignedHeaders(accessId, accessSecret, params, body);
  };

  /**
   * 验证时间戳
   * @param {string} timestamp 时间戳
   * @param {number} toleranceMinutes 容差分钟数
   * @returns {boolean}
   */
  const validateTimestamp = (timestamp, toleranceMinutes = 5) => {
    return SignUtils.isValidTimestamp(timestamp, toleranceMinutes);
  };

  return {
    createSignedClient,
    generateSignedHeaders,
    validateTimestamp,
    SignUtils,
  };
}

// 导出默认实例
export default SignUtils;
