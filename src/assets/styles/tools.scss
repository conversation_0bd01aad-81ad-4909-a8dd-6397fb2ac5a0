/* ==================
          flex布局
 ==================== */

@import "./variables.module.scss";

.v-grid {
  display: grid;
}

.v-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.v-flex-base {
  display: flex;
  flex-direction: row;
}

.v-flex-1 {
  flex: 1;
}

.v-flex-col {
  display: flex;
  flex-direction: column;
}

.v-flex-wrap {
  flex-wrap: wrap;
}

.v-flex-nowrap {
  flex-wrap: nowrap;
}

.v-col-center {
  align-items: center;
}

.v-col-top {
  align-items: flex-start;
}

.v-col-bottom {
  align-items: flex-end;
}

.v-col-stretch {
  align-items: stretch;
}

.v-row-center {
  justify-content: center;
}

.v-row-left {
  justify-content: flex-start;
}

.v-row-right {
  justify-content: flex-end;
}

.v-row-between {
  justify-content: space-between;
}

.v-row-around {
  justify-content: space-around;
}

.v-self-start {
  align-self: flex-start;
}

.v-self-end {
  align-self: flex-end;
}

.v-self-center {
  align-self: center;
}

/* ==================
 
     margin padding: 内外边距
     缩写版：v-m-r-10
     完整版：v-margin-right-10
     左右：v-m-x-10
     上下：v-m-y-10
   
  ==================== */
@for $i from 0 through 200 {
  // 只要双数和能被5除尽的数
  @if $i % 2==0 or $i % 5==0 {
    .v-margin-#{$i},
    .v-m-#{$i} {
      margin: $i + px !important;
    }
    .v-m-x-#{$i} {
      margin-left: $i + px !important;
      margin-right: $i + px !important;
    }
    .v-m-y-#{$i} {
      margin-top: $i + px !important;
      margin-bottom: $i + px !important;
    }

    .v-padding-#{$i},
    .v-p-#{$i} {
      padding: $i + px !important;
    }
    .v-p-x-#{$i} {
      padding-left: $i + px !important;
      padding-right: $i + px !important;
    }
    .v-p-y-#{$i} {
      padding-top: $i + px !important;
      padding-bottom: $i + px !important;
    }

    @each $short, $long in l left, t top, r right, b bottom {
      // 定义外边距
      .v-m-#{$short}-#{$i} {
        margin-#{$long}: $i + px !important;
      }

      // 定义内边距
      .v-p-#{$short}-#{$i} {
        padding-#{$long}: $i + px !important;
      }

      // 定义外边距
      .v-margin-#{$long}-#{$i} {
        margin-#{$long}: $i + px !important;
      }

      // 定义内边距
      .v-padding-#{$long}-#{$i} {
        padding-#{$long}: $i + px !important;
      }
    }
  }
}

/* ==================
 
     radius
   
  ==================== */
@for $i from 0 through 100 {
  // 只要双数和能被5除尽的数
  @if $i % 2==0 or $i % 5==0 {
    .v-radius-#{$i},
    .v-r-#{$i} {
      border-radius: $i + px !important;
    }

    .v-r-t-#{$i} {
      border-top-left-radius: $i + px !important;
      border-top-right-radius: $i + px !important;
    }

    .v-r-b-#{$i} {
      border-bottom-left-radius: $i + px !important;
      border-bottom-right-radius: $i + px !important;
    }

    @each $short, $long in tl "top-left", tr "top-right", bl "bottom-right",
      br "bottom-right"
    {
      // 定义外边距
      .v-r-#{$short}-#{$i} {
        border-#{$long}-radius: $i + px !important;
      }

      // 定义内边距
      .v-radius-#{$long}-#{$i} {
        border-#{$long}-radius: $i + px !important;
      }
    }
  }
}

/* ==================
 
     溢出省略号
     @param {Number} 行数
   
  ==================== */

@mixin ellipsis($rowCount: 1) {
  @if $rowCount <=1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $rowCount;
    -webkit-box-orient: vertical;
  }
}

@for $i from 1 through 6 {
  .v-line-#{$i} {
    @include ellipsis($i);
  }
}

/* ==================
 
     字体大小
   
  ==================== */

@for $i from 12 through 50 {
  .v-font-#{$i} {
    font-size: $i + px;
  }
}

/* ==================
 
     其他
   
  ==================== */

.v-border-bottom {
  border-bottom: 1px solid #e4e7ed;
}

.v-pointer {
  cursor: pointer;
}

.v-link {
  color: $--color-primary;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

.v-title {
  font-size: 16px;
  color: #393a3c;
  font-weight: bold;
  display: flex;
  align-items: center;
  &::before {
    display: inline-block;
    content: "";
    width: 4px;
    height: 20px;
    background: $--color-primary;
    border-radius: 3px;
    margin-right: 8px;
  }
}

.v-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.v-text-center {
  text-align: center;
}
