import { ref, computed, watch } from 'vue'
import { getCurrentInstance } from 'vue'

/**
 * 参数配置通用组合式函数
 * @param {Object} options 配置选项
 * @param {Function} options.loadConfigApi 加载配置的API函数
 * @param {Function} options.saveConfigApi 保存配置的API函数
 * @param {Function} options.deleteConfigApi 删除配置的API函数
 * @param {Function} options.createNewParam 创建新参数的函数
 * @param {Array} options.requiredFields 必填字段列表
 * @param {String} options.deleteParamKey 删除参数时使用的key
 */
export function useParamConfig(options) {
  const { proxy } = getCurrentInstance()
  
  // 响应式数据
  const loading = ref(false)
  const treeData = ref([])
  
  // 加载参数配置
  async function loadParamConfig(interfaceId) {
    try {
      loading.value = true
      const response = await options.loadConfigApi({
        interfaceId: interfaceId,
      })
      
      treeData.value = response.data || []
      // 如果没有数据，添加一个默认节点
      if (treeData.value.length === 0) {
        addRootParam()
      }
    } catch (error) {
      console.error('加载参数配置失败:', error)
      // 加载失败时也添加一个默认节点
      addRootParam()
    } finally {
      loading.value = false
    }
  }
  
  // 生成唯一ID
  function generateId() {
    return Date.now() + Math.random().toString(36).substring(2, 11)
  }
  
  // 添加根节点
  function addRootParam() {
    const newParam = options.createNewParam()
    treeData.value.push(newParam)
  }
  
  // 添加同级节点
  function addSiblingParam(row) {
    const newParam = options.createNewParam(row.parentId)
    
    if (row.parentId) {
      // 找到父节点并添加子节点
      const parent = findNodeById(treeData.value, row.parentId)
      if (parent) {
        parent.children.push(newParam)
        parent.hasChildren = true
      }
    } else {
      // 添加到根级别
      treeData.value.push(newParam)
    }
  }
  
  // 添加子节点
  function addChildParam(row) {
    const newParam = options.createNewParam(row.id)
    
    if (!row.children) {
      row.children = []
    }
    row.children.push(newParam)
    row.hasChildren = true
  }
  
  // 根据ID查找节点
  function findNodeById(nodes, id) {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeById(node.children, id)
        if (found) return found
      }
    }
    return null
  }
  
  // 删除节点
  async function deleteParam(row) {
    // 检查是否存在子节点
    if (row.children && row.children.length > 0) {
      proxy.$modal.msgWarning('该节点存在子节点，请先删除子节点后再试')
      return
    }
    
    try {
      await proxy.$modal.confirm('确定要删除该参数吗？')
      
      if (!row.isNew && row.id) {
        const deleteParams = {}
        deleteParams[options.deleteParamKey] = row.id
        await options.deleteConfigApi(deleteParams)
      }
      
      // 从树中删除节点
      removeNodeFromTree(treeData.value, row.id)
      proxy.$modal.msgSuccess('删除成功')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        proxy.$modal.msgError('删除失败')
      }
    }
  }
  
  // 从树中删除节点
  function removeNodeFromTree(nodes, id) {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === id) {
        nodes.splice(i, 1)
        return true
      }
      if (nodes[i].children && nodes[i].children.length > 0) {
        if (removeNodeFromTree(nodes[i].children, id)) {
          // 如果子节点被删除后没有子节点了，更新hasChildren
          if (nodes[i].children.length === 0) {
            nodes[i].hasChildren = false
          }
          return true
        }
      }
    }
    return false
  }
  
  // 收集所有节点数据（包括子节点）
  function collectAllNodes(nodes) {
    const result = []
    
    function traverse(nodeList) {
      nodeList.forEach((node) => {
        result.push(node)
        if (node.children && node.children.length > 0) {
          traverse(node.children)
        }
      })
    }
    
    traverse(nodes)
    return result
  }
  
  // 递归处理保存节点数据
  function processNodesForSave(nodes) {
    nodes.forEach((node) => {
      delete node.parentId
      // 如果是新节点且有id属性，则删除id
      if (node.isNew && node.id) {
        delete node.id
      }
      // 递归处理子节点
      if (Array.isArray(node.children) && node.children.length > 0) {
        processNodesForSave(node.children)
      }
    })
  }
  
  // 验证必填字段
  function validateRequiredFields(nodes) {
    const allNodes = collectAllNodes(nodes)
    
    for (const param of allNodes) {
      for (const field of options.requiredFields) {
        if (!param[field.key]) {
          proxy.$modal.msgError(field.message)
          return false
        }
      }
    }
    return true
  }
  
  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true
      
      // 验证必填字段
      if (!validateRequiredFields(treeData.value)) {
        return false
      }
      
      // 收集所有节点数据
      const allNodes = [...treeData.value]
      
      // 递归处理节点数据：删除新节点的id属性
      processNodesForSave(allNodes)
      
      await options.saveConfigApi(allNodes)
      
      proxy.$modal.msgSuccess('保存成功')
      return true
    } catch (error) {
      console.error('保存失败:', error)
      proxy.$modal.msgError('保存失败')
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 重置数据
  function resetData() {
    treeData.value = []
  }
  
  return {
    loading,
    treeData,
    generateId,
    loadParamConfig,
    addRootParam,
    addSiblingParam,
    addChildParam,
    deleteParam,
    handleSubmit,
    resetData,
    findNodeById,
    collectAllNodes
  }
}
