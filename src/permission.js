/*
 * @Author: lituo <EMAIL>
 * @Date: 2025-05-19 15:12:03
 * @LastEditors: lituo <EMAIL>
 * @LastEditTime: 2025-05-19 18:04:15
 * @FilePath: \zs-mzt-jzt-admin\src\permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import router from "./router";
import { ElMessage } from "element-plus";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken, removeToken } from "@/utils/auth";
import { isHttp } from "@/utils/validate";
import { isRelogin } from "@/utils/request";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import { getNormalPath } from "@/utils/ruoyi";

NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/register"];

router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title);
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        useUserStore()
          .getInfo()
          .then(() => {
            isRelogin.show = false;
            usePermissionStore()
              .generateRoutes()
              .then((accessRoutes) => {
                // 过滤出 hidden 为 false 的第一个路由
                const firstVisibleRoute = accessRoutes.find(
                  (route) => !route.hidden
                );
                let firstPath = firstVisibleRoute.path;
                const firstChild = firstVisibleRoute.children?.find(
                  (child) => !child.hidden
                );
                if (firstChild) {
                  // firstPath += "/" + firstChild.path;
                  firstPath = getNormalPath(
                    `${firstVisibleRoute.path}/${firstChild.path}`
                  );
                }
                // 根据roles权限生成可访问的路由表
                accessRoutes.forEach((route) => {
                  if (!isHttp(route.path)) {
                    router.addRoute(route); // 动态添加可访问路由表
                  }
                });
                // 如果找到第一个可见的路由，则跳转
                if (
                  firstVisibleRoute &&
                  (to.path === "/" ||
                    to.path == "/login" ||
                    to.path == "/index")
                ) {
                  next({ path: firstPath, replace: true });
                } else {
                  // 如果没有找到可见路由，默认跳转到首页或其他指定页面
                  next({ ...to, replace: true });
                }
              });
          })
          .catch((err) => {
            removeToken();
            useUserStore()
              .logOut()
              .then(() => {
                ElMessage.error(err);
                next({ path: "/login" });
              });
          });
      } else {
        next();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
