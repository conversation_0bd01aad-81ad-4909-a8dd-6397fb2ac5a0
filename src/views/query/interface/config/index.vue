<template>
  <el-row class="app-container" :gutter="20">
    <el-col :span="6">
      <div class="panel-content">
        <div class="search-box">
          <el-input
            v-model="categorySearchText"
            placeholder="请输入目录分类名称"
            clearable
            @input="handleCategorySearch"
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="tree-content">
          <el-tree
            ref="categoryTreeRef"
            :data="categoryTreeData"
            :props="{ children: 'children', label: 'cateName' }"
            :filter-node-method="filterCategoryNode"
            node-key="id"
            default-expand-all
            @node-click="handleCategoryClick"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-tooltip
                  :content="node.label"
                  show-after="800"
                  placement="top"
                >
                  <span class="tree-node-label v-line-1">{{ node.label }}</span>
                </el-tooltip>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
    </el-col>

    <!-- 右侧内容区 - span设置为18 -->
    <el-col :span="18">
      <div class="panel-content">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="searchText"
            placeholder="请输入接口名称"
            clearable
            style="width: 300px"
            @input="handleSearch"
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 接口列表表格 -->
        <el-table
          v-loading="loading"
          :data="interfaceList"
          style="width: 100%"
          border
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty description="暂无接口数据" />
          </template>
          <el-table-column type="index" label="序号" width="55" />
          <el-table-column prop="id" label="ID" width="180" />
          <el-table-column
            prop="interfaceName"
            label="接口名称"
            min-width="150"
          />

          <el-table-column
            label="接口分类"
            prop="type"
            width="100"
            align="center"
          >
            <template #default="scope">
              <dict-tag
                :options="interface_type"
                :value="scope.row.interfaceType"
              />
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="0"
                :inactive-value="1"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="description"
            label="接口描述"
            min-width="200"
          />
          <el-table-column label="操作" width="230" fixed="right">
            <template #default="{ row }">
              <div class="menu-handel">
                <el-button type="primary" link @click="handleConfigInput(row)">
                  设置入参
                </el-button>
                <el-button type="primary" link @click="handleConfigOutput(row)">
                  设置出参
                </el-button>
                <el-button type="primary" link @click="handleConfigRecord(row)">
                  配置记录
                </el-button>
                <el-button type="primary" link @click="handleConfigHeader(row)">
                  设置请求头配置
                </el-button>
                <el-button
                  type="primary"
                  link
                  @click="handleConfigResponse(row)"
                >
                  设置响应配置
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getInterfaceList"
        />
      </div>
    </el-col>

    <!-- 各种弹框组件保持不变 -->
    <ConfigInput
      v-model="inputDialogVisible"
      :interface-data="currentInterface"
      @success="handleConfigSuccess"
    />

    <!-- 设置出参弹框 -->
    <ConfigOutput
      v-model="outputDialogVisible"
      :interface-data="currentInterface"
      @success="handleConfigSuccess"
    />

    <!-- 设置请求头弹框 -->
    <ConfigHeader
      v-model="headerDialogVisible"
      :interface-data="currentInterface"
      @success="handleConfigSuccess"
    />

    <!-- 设置响应配置弹框 -->
    <ConfigResponse
      v-model="responseDialogVisible"
      :interface-data="currentInterface"
      @success="handleConfigSuccess"
    />

    <!-- 配置记录弹框 -->
    <ConfigRecord
      v-model="recordDialogVisible"
      :interface-data="currentInterface"
    />
  </el-row>
</template>

<script setup name="InterfaceConfig">
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { Search } from "@element-plus/icons-vue";
import {
  getSearchTreeData,
  updateApiInterfaceStatus,
} from "@/api/query/config";
import { getApiInterfaceList } from "@/api/query/interface";
import ConfigInput from "./components/configIn.vue";
import ConfigOutput from "./components/configOut.vue";
import ConfigHeader from "./components/configHeader.vue";
import ConfigResponse from "./components/configResponse.vue";
import ConfigRecord from "./components/configRecord.vue";

const { proxy } = getCurrentInstance();
const { sys_oper_type, interface_type } = proxy.useDict(
  "sys_oper_type",
  "interface_type"
);

// 响应式数据
const loading = ref(false);
const categorySearchText = ref("");
const searchText = ref("");
const categoryTreeData = ref([]);
const interfaceList = ref([]);
const selectedRows = ref([]);
const total = ref(0);
const currentInterface = ref(null);

// 弹框显示状态
const inputDialogVisible = ref(false);
const outputDialogVisible = ref(false);
const headerDialogVisible = ref(false);
const responseDialogVisible = ref(false);
const recordDialogVisible = ref(false);

// 查询参数
const queryParams = reactive({
  current: 1,
  size: 10,
  name: "",
  interfaceCata: null,
});

// 引用
const categoryTreeRef = ref();

// 获取分类树数据
async function getCategoryTree() {
  try {
    const response = await getSearchTreeData();
    categoryTreeData.value = response.data || [];
  } catch (error) {
    console.error("获取分类树失败:", error);
  }
}

// 获取接口列表
async function getInterfaceList() {
  try {
    loading.value = true;
    const { data } = await getApiInterfaceList(queryParams);
    interfaceList.value = data.records || [];
    total.value = data.total || 0;
  } catch (error) {
    console.error("获取接口列表失败:", error);
  } finally {
    loading.value = false;
  }
}

// 分类搜索
function handleCategorySearch() {
  categoryTreeRef.value?.filter(categorySearchText.value);
}

// 分类树过滤方法
function filterCategoryNode(value, data) {
  if (!value) return true;
  return data.cateName.indexOf(value) !== -1;
}

// 分类点击事件
function handleCategoryClick(data) {
  queryParams.interfaceCata = data.id;
  queryParams.current = 1;
  getInterfaceList();
}

// 接口搜索
function handleSearch() {
  queryParams.interfaceName = searchText.value;
  queryParams.current = 1;
  getInterfaceList();
}

// 表格选择变化
function handleSelectionChange(selection) {
  selectedRows.value = selection;
}

// 状态切换
async function handleStatusChange(row) {
  try {
    await updateApiInterfaceStatus({
      id: row.id,
      status: row.status,
    });
    proxy.$modal.msgSuccess("状态更新成功");
  } catch (error) {
    console.error("状态更新失败:", error);
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1;
    proxy.$modal.msgError("状态更新失败");
  }
}

// 设置入参
function handleConfigInput(row) {
  currentInterface.value = row;
  inputDialogVisible.value = true;
}

// 设置出参
function handleConfigOutput(row) {
  currentInterface.value = row;
  outputDialogVisible.value = true;
}

// 设置请求头
function handleConfigHeader(row) {
  currentInterface.value = row;
  headerDialogVisible.value = true;
}

// 设置响应配置
function handleConfigResponse(row) {
  currentInterface.value = row;
  responseDialogVisible.value = true;
}

// 配置记录
function handleConfigRecord(row) {
  currentInterface.value = row;
  recordDialogVisible.value = true;
}

// 配置成功回调
function handleConfigSuccess() {
  getInterfaceList();
}

// 页面初始化
onMounted(async () => {
  await getCategoryTree();
  await getInterfaceList();
});
</script>

<style scoped lang="scss">
.app-container {
  padding: 10px;
  height: calc(100vh - 120px);
  margin: 0 !important;
}

.panel-content {
  height: 100%;
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

// 左侧面板样式已通过panel-content应用

// 右侧面板样式已通过panel-content应用

.tree-content {
  flex: 1;
  overflow-y: auto;

  .tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .tree-node-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 150px;
    }

    .tree-node-actions {
      opacity: 0;
      transition: opacity 0.3s;

      .el-button {
        padding: 2px;
        margin-left: 2px;
      }
    }

    &:hover .tree-node-actions {
      opacity: 1;
    }
  }
}

.menu-handel {
  .el-button {
    padding: 2px !important;
    margin-right: 1px !important;
    margin-left: 1px !important;
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-table) {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-tree-node__content) {
  height: 36px;
  line-height: 36px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(
    .el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content
  ) {
  background-color: #409eff;
  color: #fff;
}
</style>
