<template>
  <el-dialog
    v-model="visible"
    :width="isFullscreen ? '100%' : '90%'"
    :fullscreen="isFullscreen"
    :before-close="handleClose"
    destroy-on-close
    class="config-dialog"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <span :id="titleId" :class="titleClass">设置出参</span>
        <div class="header-actions">
          <el-tooltip
            :content="isFullscreen ? '退出全屏' : '全屏显示'"
            placement="bottom"
          >
            <el-button
              type="primary"
              text
              @click="toggleFullscreen"
              class="fullscreen-btn"
            >
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <Aim v-else />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <!-- 查询结果参数配置 -->

    <!-- 添加根节点按钮 -->
    <div class="toolbar">
      <el-button type="primary" @click="addRootParam">
        <el-icon><Plus /></el-icon>
        添加根节点
      </el-button>
    </div>
    <div
      class="config-section"
      v-loading="loading"
      element-loading-text="加载配置数据中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.75)"
    >
      <!-- 使用 el-table 实现树形结构 -->
      <el-table
        :data="treeData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="true"
        border
        class="param-table"
      >
        <template #empty>
          <el-empty description="暂数据" />
        </template>
        <el-table-column label="参数名称" min-width="150">
          <template #default="{ row }">
            <el-input v-model="row.fieldName" placeholder="请输入参数名称" />
          </template>
        </el-table-column>

        <el-table-column label="中文名称" min-width="150">
          <template #default="{ row }">
            <el-input v-model="row.chineseName" placeholder="请输入中文名称" />
          </template>
        </el-table-column>

        <el-table-column label="参数类型" width="120">
          <template #default="{ row }">
            <el-select
              v-model="row.dataType"
              placeholder="请选择"
              style="width: 100%"
              @change="handleTypeChange(row)"
            >
              <el-option
                v-for="dict in param_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="字典类型" width="180">
          <template #default="{ row }">
            <el-select
              v-model="row.dictType"
              placeholder="请选择字典类型"
              style="width: 100%"
              :disabled="row.dataType !== 'dictionary'"
              filterable
              clearable
              :loading="dictTypesLoading"
            >
              <el-option
                v-for="dict in dictTypes"
                :key="dict.id"
                :label="dict.dictName"
                :value="dict.id"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="是否加密" width="100">
          <template #default="{ row }">
            <el-select
              v-model="row.secreted"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="脱敏规则" width="120">
          <template #default="{ row }">
            <el-select
              v-model="row.validationRule"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="dict in validation_rule"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="是否展示" width="100">
          <template #default="{ row }">
            <el-select
              v-model="row.showed"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="说明" min-width="150">
          <template #default="{ row }">
            <el-input v-model="row.description" placeholder="请输入说明" />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="230" fixed="right">
          <template #default="{ row }">
            <div class="menu-handel">
              <el-button type="primary" link @click="addSiblingParam(row)">
                添加同级节点
              </el-button>
              <el-button type="success" link @click="addChildParam(row)">
                添加子节点
              </el-button>

              <el-button type="danger" link @click="deleteParam(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleFormSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ConfigOutput">
import { ref, onMounted } from "vue";
import { FullScreen, Aim } from "@element-plus/icons-vue";
import {
  getSearchOutputConfig,
  saveOrUpdateResponseMappings,
  deleteSearchOutputConfig,
} from "@/api/query/config";
import { typeListOptions } from "@/api/system/dict/type";
import { useParamConfig } from "@/composables/useParamConfig";

const { proxy } = getCurrentInstance();
const { validation_rule, param_type } = proxy.useDict(
  "validation_rule",
  "param_type"
);

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  interfaceData: {
    type: Object,
    default: null,
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue", "success"]);

// 全屏状态
const isFullscreen = ref(false);

// 字典类型相关
const dictTypes = ref([]);
const dictTypesLoading = ref(false);

// 切换全屏
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;
}

// 加载字典类型列表
async function loadDictTypes() {
  try {
    dictTypesLoading.value = true;
    const response = await typeListOptions();

    if (response.code === 0) {
      dictTypes.value = response.data || [];
    } else {
      proxy.$modal.msgError("获取字典类型失败");
    }
  } catch (error) {
    console.error("加载字典类型失败:", error);
    proxy.$modal.msgError("加载字典类型失败");
  } finally {
    dictTypesLoading.value = false;
  }
}

// 处理参数类型变化
function handleTypeChange(row) {
  if (row.dataType !== "dictionary") {
    // 当参数类型不是 dictionary 时，清空字典类型
    row.dictType = "";
  }
}

// 创建新参数的函数
function createNewParam(parentId = null) {
  return {
    id: Date.now() + Math.random().toString(36).substring(2, 11),
    interfaceId: props.interfaceData?.id,
    fieldName: "",
    chineseName: "",
    dataType: "string",
    validationRule: "",
    description: "",
    parentId,
    showed: 1,
    secreted: 0,
    sort: 0,
    children: [],
    hasChildren: false,
    isNew: true,
  };
}

// 使用参数配置组合式函数
const {
  loading,
  treeData,
  loadParamConfig,
  addRootParam,
  addSiblingParam,
  addChildParam,
  deleteParam,
  handleSubmit,
  resetData,
} = useParamConfig({
  loadConfigApi: getSearchOutputConfig,
  saveConfigApi: saveOrUpdateResponseMappings,
  deleteConfigApi: deleteSearchOutputConfig,
  createNewParam,
  requiredFields: [
    { key: "fieldName", message: "请填写参数名称" },
    { key: "chineseName", message: "请填写中文名称" },
  ],
  deleteParamKey: "responseMappingId",
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 监听弹框显示
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.interfaceData) {
      loadParamConfig(props.interfaceData.id);
    }
  }
);

// 提交表单处理
async function handleFormSubmit() {
  const success = await handleSubmit();
  if (success) {
    emit("success");
    handleClose();
  }
}

// 关闭弹框
function handleClose() {
  visible.value = false;
  resetData();
}

// 组件初始化
onMounted(() => {
  loadDictTypes();
});
</script>

<style scoped lang="scss">
.config-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .header-actions {
      display: flex;
      gap: 8px;
      position: relative;
      top: -10px;

      .fullscreen-btn {
        padding: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f0f8ff;
          color: #409eff;
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.config-section {
  margin-bottom: 20px;
  min-height: 400px;
  position: relative;

  // Loading 状态样式优化
  :deep(.el-loading-mask) {
    border-radius: 6px;
  }

  :deep(.el-loading-text) {
    color: #409eff;
    font-size: 14px;
    margin-top: 10px;
  }

  :deep(.el-loading-spinner) {
    .circular {
      width: 42px;
      height: 42px;
    }
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #409eff;

  .el-icon {
    margin-right: 8px;
  }
}

.toolbar {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.param-table {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :deep(.el-table__header) {
    background-color: #f5f7fa;

    th {
      background-color: #f5f7fa !important;
      color: #606266;
      font-weight: 600;
      border-bottom: 2px solid #e4e7ed;
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #f8f9fa;
      }
    }

    td {
      padding: 12px 8px;
      border-bottom: 1px solid #f0f0f0;

      .el-input {
        .el-input__wrapper {
          border-radius: 4px;
          box-shadow: 0 0 0 1px #dcdfe6 inset;
          transition: all 0.2s;

          &:hover {
            box-shadow: 0 0 0 1px #c0c4cc inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #409eff inset;
          }
        }
      }

      .el-select {
        width: 100%;

        .el-input__wrapper {
          border-radius: 4px;
        }
      }

      .el-button {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }

        &.is-link {
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;

          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
          }

          &.el-button--success:hover {
            background-color: rgba(103, 194, 58, 0.1);
          }

          &.el-button--danger:hover {
            background-color: rgba(245, 108, 108, 0.1);
          }
        }
      }
    }
  }

  :deep(.el-table__expand-icon) {
    color: #409eff;
    font-size: 14px;
    margin-top: 10px;
  }
}

:deep(.el-table .cell) {
  display: flex;
}

.dialog-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;

  .el-button {
    min-width: 80px;
    margin: 0 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .param-table {
    :deep(.el-table__body) {
      td {
        padding: 8px 4px;

        .el-input,
        .el-select {
          .el-input__wrapper {
            font-size: 12px;
          }
        }

        .el-button {
          font-size: 11px;
          padding: 2px 6px;
        }
      }
    }
  }
}

.menu-handel {
  .el-button {
    padding: 2px !important;
    margin-right: 1px !important;
    margin-left: 1px !important;
  }
}
</style>
