<template>
  <el-dialog
    v-model="visible"
    title="配置记录"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <!-- 配置记录 -->
    <div class="config-section">
      <el-table
        border
        v-loading="loading"
        :data="recordList"
        style="width: 100%"
      >
        <template #empty>
          <el-empty description="暂无配置记录" />
        </template>
        <el-table-column prop="id" label="序号" width="80" />
        <el-table-column prop="name" label="接口名称" min-width="150" />
        <el-table-column prop="operationType" label="操作类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTag(row.operationType)">
              {{ getOperationTypeText(row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="配置人员" width="120" />
        <el-table-column prop="operationTime" label="配置时间" width="180">
          <template #default="{ row }">
            {{ parseTime(row.operationTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>
        <el-table-column
          prop="operationDetail"
          label="操作详情"
          min-width="200"
        />
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getRecordList"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleClose">确定</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ConfigRecord">
import { ref, reactive, computed, watch, getCurrentInstance } from "vue";
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  interfaceData: {
    type: Object,
    default: null,
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue"]);

// 响应式数据
const loading = ref(false);
const recordList = ref([]);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  interfaceId: null,
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 监听弹框显示
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.interfaceData) {
      queryParams.interfaceId = props.interfaceData.id;
      getRecordList();
    }
  }
);

// 获取配置记录列表
async function getRecordList() {
  try {
    loading.value = true;
    // 这里应该调用获取配置记录的API
    // const response = await getConfigRecordList(queryParams);
    // recordList.value = response.rows || [];
    // total.value = response.total || 0;
  } catch (error) {
    console.error("获取配置记录失败:", error);
  } finally {
    loading.value = false;
  }
}

// 获取操作类型标签
function getOperationTypeTag(type) {
  const tagMap = {
    设置入参: "primary",
    设置出参: "success",
    设置请求头: "warning",
    设置响应配置: "info",
  };
  return tagMap[type] || "default";
}

// 获取操作类型文本
function getOperationTypeText(type) {
  return type;
}

// 关闭弹框
function handleClose() {
  visible.value = false;
  recordList.value = [];
  total.value = 0;
}
</script>

<style scoped lang="scss">
.config-section {
  margin-bottom: 20px;
  padding: 20px;
}

.dialog-footer {
  text-align: center;
}
</style>
