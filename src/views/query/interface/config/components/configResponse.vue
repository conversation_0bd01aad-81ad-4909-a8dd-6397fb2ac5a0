<template>
  <el-dialog
    v-model="visible"
    title="设置响应配置"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <!-- 设置响应配置 -->
    <div class="config-section">
      <el-form
        ref="formRef"
        :model="responseConfig"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="成功状态码字段" prop="successCodeField" required>
          <el-input
            v-model="responseConfig.successCodeField"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label="成功状态码" prop="successCode">
          <el-input v-model="responseConfig.successCode" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="消息码字段" prop="successMessage" required>
          <el-input
            v-model="responseConfig.successMessage"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label="错误码字段" prop="errorCodeField">
          <el-input
            v-model="responseConfig.errorCodeField"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label="错误消息字段" prop="errorMessageField" required>
          <el-input
            v-model="responseConfig.errorMessageField"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ConfigResponse">
import { ref, reactive, computed, watch, getCurrentInstance } from "vue";
import { Setting } from "@element-plus/icons-vue";
import {
  getSearchResponseConfig,
  saveOrUpdateSearchResponseConfig,
} from "@/api/query/config";

const { proxy } = getCurrentInstance();

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  interfaceData: {
    type: Object,
    default: null,
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue", "success"]);

// 响应式数据
const loading = ref(false);
const formRef = ref();

// 响应配置表单
const responseConfig = reactive({
  id: null,
  interfaceId: null,
  successCodeField: "",
  successCode: "",
  successMessage: "",
  errorCodeField: "",
  errorMessageField: "",
});

// 表单验证规则
const rules = {
  successCodeField: [
    { required: true, message: "请输入成功状态码字段", trigger: "blur" },
  ],
  successCode: [
    { required: true, message: "请输入成功状态码", trigger: "blur" },
  ],
  successMessage: [
    { required: true, message: "请输入消息码", trigger: "blur" },
  ],
  errorCodeField: [
    { required: true, message: "请输入消息码字段", trigger: "blur" },
  ],
  errorMessageField: [
    { required: true, message: "请输入错误消息字段", trigger: "blur" },
  ],
};

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 监听弹框显示
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.interfaceData) {
      loadResponseConfig();
    }
  }
);

// 加载响应配置
async function loadResponseConfig() {
  try {
    loading.value = true;
    const response = await getSearchResponseConfig({
      interfaceId: props.interfaceData.id,
    });

    if (response.data) {
      Object.assign(responseConfig, response.data);
    } else {
      // 重置表单
      Object.assign(responseConfig, {
        id: null,
        interfaceId: props.interfaceData.id,
        successCodeField: "",
        successCode: "",
        errorCodeField: "",
        errorMessageField: "",
      });
    }
  } catch (error) {
    console.error("加载响应配置失败:", error);
  } finally {
    loading.value = false;
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();
    loading.value = true;

    const data = {
      ...responseConfig,
      interfaceId: props.interfaceData.id,
    };

    await saveOrUpdateSearchResponseConfig(data);
    proxy.$modal.msgSuccess("保存成功");
    emit("success");
    handleClose();
  } catch (error) {
    console.error("保存失败:", error);
    proxy.$modal.msgError("保存失败");
  } finally {
    loading.value = false;
  }
}

// 关闭弹框
function handleClose() {
  visible.value = false;
  formRef.value?.resetFields();
}
</script>

<style scoped lang="scss">
.config-section {
  margin-bottom: 20px;
  padding: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #409eff;

  .el-icon {
    margin-right: 8px;
  }
}

.dialog-footer {
  text-align: center;
}
</style>
