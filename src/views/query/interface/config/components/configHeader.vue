<template>
  <el-dialog
    v-model="visible"
    title="设置请求头"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <!-- 设置请求头 -->
    <div class="config-section">
      <div class="header-table">
        <div class="table-header">
          <div class="header-cell">请求头名称</div>
          <div class="header-cell">请求头值</div>
          <div class="header-cell">描述</div>
          <div class="header-cell">是否加密</div>
          <div class="header-cell">是否必须</div>
          <div class="header-cell" style="width: 120px">操作</div>
        </div>

        <div class="table-body">
          <div
            v-for="(header, index) in headerList"
            :key="header.id || index"
            class="table-row"
          >
            <!-- 请求头名称 -->
            <div class="table-cell">
              <el-input v-model="header.name" placeholder="请输入请求头名称" />
            </div>

            <!-- 请求头值 -->
            <div class="table-cell">
              <el-input v-model="header.value" placeholder="请输入请求头值" />
            </div>

            <!-- 描述 -->
            <div class="table-cell">
              <el-input v-model="header.description" placeholder="请输入描述" />
            </div>

            <!-- 是否加密 -->
            <div class="table-cell">
              <el-select
                v-model="header.secreted"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </div>

            <!-- 是否必须 -->
            <div class="table-cell">
              <el-select
                v-model="header.required"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </div>

            <!-- 操作 -->
            <div
              class="table-cell"
              style="width: 120px; justify-content: center"
            >
              <el-button type="primary" link @click="addHeader(index)">
                添加同级节点
              </el-button>
              <el-button
                type="danger"
                link
                @click="deleteHeader(header, index)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ConfigHeader">
import { ref, computed, watch, getCurrentInstance } from "vue";
import { Setting } from "@element-plus/icons-vue";
import {
  getSearchHeaderConfig,
  addSearchHeaderConfig,
  updateSearchHeaderConfig,
  deleteSearchHeaderConfig,
} from "@/api/query/config";

const { proxy } = getCurrentInstance();

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  interfaceData: {
    type: Object,
    default: null,
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue", "success"]);

// 响应式数据
const loading = ref(false);
const headerList = ref([]);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 监听弹框显示
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.interfaceData) {
      loadHeaderConfig();
    }
  }
);

// 加载请求头配置
async function loadHeaderConfig() {
  try {
    loading.value = true;
    const response = await getSearchHeaderConfig({
      interfaceId: props.interfaceData.id,
    });
    headerList.value = response.data || [];
    if (headerList.value.length === 0) {
      initDefaultHeader();
    }
  } catch (error) {
    console.error("加载请求头配置失败:", error);
  } finally {
    loading.value = false;
  }
}

// 创建新请求头
function createNewHeader() {
  return {
    id: Date.now() + Math.random(),
    interfaceId: props.interfaceData.id,
    name: "",
    value: "",
    description: "",
    secreted: 0,
    required: 0,
    isNew: true,
  };
}

// 添加请求头
function addHeader(index) {
  const newHeader = createNewHeader();
  headerList.value.splice(index + 1, 0, newHeader);
}

// 删除请求头
function deleteHeader(header, index) {
  proxy.$modal
    .confirm("确定要删除该请求头吗？")
    .then(async () => {
      if (!header.isNew) {
        await deleteSearchHeaderConfig({ headerId: header.id });
      }
      headerList.value.splice(index, 1);
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

// 提交表单
async function handleSubmit() {
  try {
    loading.value = true;

    // 验证必填字段
    for (const header of headerList.value) {
      if (!header.name) {
        proxy.$modal.msgError("请填写请求头名称");
        return;
      }
      if (!header.value) {
        proxy.$modal.msgError("请填写请求头值");
        return;
      }
    }

    // 保存请求头配置
    for (const header of headerList.value) {
      const data = {
        ...header,
        interfaceId: props.interfaceData.id,
      };

      if (header.isNew) {
        delete data.isNew;
        delete data.id;
        await addSearchHeaderConfig(data);
      } else {
        await updateSearchHeaderConfig(data);
      }
    }

    proxy.$modal.msgSuccess("保存成功");
    emit("success");
    handleClose();
  } catch (error) {
    console.error("保存失败:", error);
    proxy.$modal.msgError("保存失败");
  } finally {
    loading.value = false;
  }
}

// 关闭弹框
function handleClose() {
  visible.value = false;
  headerList.value = [];
}

// 初始化默认请求头
function initDefaultHeader() {
  headerList.value.push(createNewHeader());
}

// 监听接口数据变化
watch(
  () => props.interfaceData,
  (val) => {
    if (val && props.modelValue) {
      initDefaultHeader();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.config-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #409eff;

  .el-icon {
    margin-right: 8px;
  }
}

.header-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.header-cell {
  flex: 1;
  padding: 12px 8px;
  font-weight: 500;
  color: #606266;
  border-right: 1px solid #ebeef5;
  text-align: center;
  min-width: 120px;

  &:last-child {
    border-right: none;
    min-width: 180px;
  }
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  flex: 1;
  padding: 8px;
  border-right: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  min-width: 120px;

  &:last-child {
    border-right: none;
    min-width: 180px;
  }
}

.dialog-footer {
  text-align: center;
}
</style>
