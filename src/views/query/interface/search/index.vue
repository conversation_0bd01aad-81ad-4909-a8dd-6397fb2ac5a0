<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧树形组件 -->
      <el-col :span="5" :xs="24">
        <div class="tree-container">
          <div class="tree-header">
            <el-cascader
              style="width: 100%"
              @change="handleCascaderChange"
              v-model="leftSideState.selectedCascaderValue"
              placeholder="请选择接口目录搜索过滤"
              filterable
              clearable
              :options="leftSideData.cascaderOptions"
              :props="leftSideConfig.cascaderProps"
              :loading="leftSideState.cascaderLoading"
            />
          </div>
          <div class="tree-content">
            <el-tree
              ref="treeRef"
              :data="leftSideData.treeList"
              :props="leftSideConfig.treeProps"
              :expand-on-click-node="false"
              node-key="id"
              highlight-current
              default-expand-all
              @node-click="handleApiClick"
              v-loading="leftSideState.treeLoading"
            />
          </div>
        </div>
      </el-col>

      <!-- 右侧内容区域 -->
      <el-col :span="19" :xs="24">
        <div class="right-content">
          <!-- 标签页 -->
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <!-- 单条查询标签页 -->
            <el-tab-pane label="单条查询" name="single">
              <SingleQuery />
            </el-tab-pane>

            <!-- 批量查询标签页 -->
            <el-tab-pane label="批量查询" name="batch">
              <BatchQuery
                :currentInterfaceData="currentInterfaceData"
                ref="batchQueryRef"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, provide } from "vue";
import { useRouter } from "vue-router";
import {
  getSearchTreeData,
  getSearchFilterConfig,
  getSearchTableColumns,
  getSearchTableData,
  getSearchDetail,
} from "@/api/query/search";

import { getApiInterfaceList } from "@/api/query/interface";

import { itemListOptions } from "@/api/system/dict/data";

// 导入子组件
import SingleQuery from "./components/SingleQuery.vue";
import BatchQuery from "./components/BatchQuery.vue";

const { proxy } = getCurrentInstance();
const router = useRouter();

// ==================== 标签页相关 ====================
const activeTab = ref("single");
const batchQueryRef = ref(null);
const currentInterfaceId = ref(null);
const currentInterfaceData = ref({});

// ==================== 左侧筛选相关 ====================
// 左侧树形组件状态
const leftSideState = ref({
  treeLoading: false,
  cascaderLoading: false,
  selectedNodeId: null,
  selectedCascaderValue: null,
});

// 左侧数据源
const leftSideData = ref({
  cascaderOptions: [], // 级联选择器数据
  treeList: [], // 树形列表数据
});

// 左侧组件配置
const leftSideConfig = {
  treeProps: {
    label: "interfaceName",
    children: "children",
    value: "id",
  },
  cascaderProps: {
    expandTrigger: "hover",
    value: "id",
    checkStrictly: true,
    label: "cateName",
  },
};

// ==================== 右侧内容相关 ====================
// 右侧加载状态
const rightSideState = ref({
  filterLoading: false,
  tableLoading: false,
  isInitialized: false,
});

// 右侧数据源
const rightSideData = ref({
  filterConfig: [], // 筛选配置
  tableColumns: [], // 表格列配置
  tableData: [], // 表格数据
  total: 0, // 数据总数
});

// 查询参数
const queryParams = ref({
  pages: 1,
  size: 10,
  interfaceId: null, // 当前选中的接口ID
  params: {},
});

// 其他配置
const showActions = ref(false);
const treeRef = ref(null);

// ==================== 左侧筛选相关方法 ====================

/**
 * 获取级联选择器数据（接口分类树）
 */
async function loadCascaderData() {
  try {
    leftSideState.value.cascaderLoading = true;
    const response = await getSearchTreeData();
    leftSideData.value.cascaderOptions = response.data || [];
  } catch (error) {
    console.error("获取接口分类数据失败:", error);
    proxy.$modal.msgError("获取接口分类数据失败");
  } finally {
    leftSideState.value.cascaderLoading = false;
  }
}

/**
 * 处理级联选择器变化事件
 * @param {Array} selectedValues - 选中的值数组
 */
function handleCascaderChange(selectedValues = []) {
  const selectedCategoryId = selectedValues[selectedValues.length - 1];
  leftSideState.value.selectedCascaderValue = selectedValues;

  // 根据选中的分类加载对应的接口列表
  // loadApiList 函数会自动选中第一个树节点并触发右侧数据加载
  loadApiList(selectedCategoryId);
}

/**
 * 获取API接口列表
 * @param {string|number} categoryId - 分类ID（可选）
 */
async function loadApiList(categoryId) {
  try {
    leftSideState.value.treeLoading = true;
    const response = await getApiInterfaceList({
      size: 1000,
      current: 1,
      status: 0,
      interfaceCata: categoryId ?? "",
    });
    leftSideData.value.treeList = response.data.records || [];

    // 等待 DOM 更新完成后自动选中 el-tree 的第一个节点
    await nextTick();
    if (leftSideData.value.treeList.length > 0) {
      const firstNode = leftSideData.value.treeList[0];

      // 设置树形组件的当前选中节点（视觉高亮）
      treeRef.value?.setCurrentKey(firstNode.id);

      // 更新左侧选中状态
      leftSideState.value.selectedNodeId = firstNode.id;

      // 触发右侧数据加载
      await handleApiClick(firstNode);
    }
  } catch (error) {
    console.error("获取API列表失败:", error);
    proxy.$modal.msgError("获取API列表失败");
  } finally {
    leftSideState.value.treeLoading = false;
  }
}

// ==================== 右侧内容相关方法 ====================

/**
 * 获取筛选配置
 * @param {string|number} interfaceId - 接口ID
 */
async function loadFilterConfig(interfaceId) {
  queryParams.value.params = {};
  try {
    rightSideState.value.filterLoading = true;
    const response = await getSearchFilterConfig({ interfaceId });
    const config = response.data || [];

    rightSideData.value.filterConfig = config;

    // 初始化查询参数
    config.forEach((filter) => {
      if (!queryParams.value.hasOwnProperty(filter.name)) {
        queryParams.value["params"][filter.name] =
          filter.type === "daterange" ? [] : "";
      }
    });

    // 预加载字典数据
    const dictFilters = config.filter(
      (filter) => filter.type === "dictionary" && filter.dictType
    );
    for (const filter of dictFilters) {
      await loadDictOptions(filter.dictType);
    }
  } catch (error) {
    console.error("获取筛选配置失败:", error);
    proxy.$modal.msgError("获取筛选配置失败");
    const config = [];

    rightSideData.value.filterConfig = config;

    // 初始化查询参数
    config.forEach((filter) => {
      if (!queryParams.value.hasOwnProperty(filter.field)) {
        queryParams.value[filter.field] = filter.type === "daterange" ? [] : "";
      }
    });
  } finally {
    rightSideState.value.filterLoading = false;
  }
}

/**
 * 获取表格列配置
 * @param {string|number} interfaceId - 接口ID
 */
async function loadTableColumns(interfaceId) {
  try {
    const response = await getSearchTableColumns({ interfaceId });
    const columns = response.data || [];

    rightSideData.value.tableColumns = columns;

    // 预加载表格列中的字典数据
    const dictColumns = columns.filter(
      (column) => column.dataType === "dictionary" && column.dictType
    );
    for (const column of dictColumns) {
      await loadDictOptions(column.dictType);
    }
  } catch (error) {
    console.error("获取表格列配置失败:", error);
    proxy.$modal.msgError("获取表格列配置失败");
    // 使用模拟配置作为降级方案
    rightSideData.value.tableColumns = [];
  }
}

/**
 * 获取表格数据
 * @param {string|number} interfaceId - 接口ID
 */
async function loadTableData(interfaceId) {
  try {
    rightSideState.value.tableLoading = true;

    // 构建查询参数
    const params = {
      ...queryParams.value,
      interfaceId,
    };

    const response = await getSearchTableData(params);
    rightSideData.value.tableData = response.data || [];
    rightSideData.value.total = response.data.total || 0;
  } catch (error) {
    console.error("获取表格数据失败:", error);
    proxy.$modal.msgError("获取表格数据失败");

    rightSideData.value.tableData = [];
    rightSideData.value.total = 0;
  } finally {
    rightSideState.value.tableLoading = false;
  }
}

// ==================== 左右侧联动逻辑 ====================

/**
 * 处理API节点点击事件（核心联动逻辑）
 * @param {Object} nodeData - 树节点数据
 */
async function handleApiClick(nodeData) {
  try {
    // 更新选中状态
    leftSideState.value.selectedNodeId = nodeData.id;
    queryParams.value.interfaceId = nodeData.id;
    queryParams.value.pages = 1;

    // 更新当前接口ID
    currentInterfaceId.value = nodeData.id;
    currentInterfaceData.value = nodeData;

    // 如果是用户手动点击（而非程序自动调用），需要设置树的选中状态
    if (treeRef.value && treeRef.value.getCurrentKey() !== nodeData.id) {
      treeRef.value.setCurrentKey(nodeData.id);
    }

    // 并行加载右侧三个接口的数据
    await Promise.all([
      loadFilterConfig(nodeData.id), // 第一个接口：获取筛选条件数据
      loadTableColumns(nodeData.id), // 第二个接口：获取表格配置列信息
      // loadTableData(nodeData.id), // 第三个接口：获取表格数据
    ]);

    // 标记右侧已初始化
    rightSideState.value.isInitialized = true;
    rightSideData.value.tableData = [];
    rightSideData.value.total = 0;

    // 通知批量查询组件接口变化
    if (batchQueryRef.value && batchQueryRef.value.onInterfaceChange) {
      batchQueryRef.value.onInterfaceChange();
    }
  } catch (error) {
    console.error("加载接口数据失败:", error);
    proxy.$modal.msgError("加载接口数据失败");
  }
}

/**
 * 处理表格数据重新加载（用于分页、查询等）
 */
async function handleTableDataLoad() {
  if (!queryParams.value.interfaceId) {
    proxy.$modal.msgWarning("请先选择接口");
    return;
  }
  await loadTableData(queryParams.value.interfaceId);
}

// ==================== 标签页相关方法 ====================

/**
 * 处理标签页切换
 * @param {string} tabName - 标签页名称
 */
function handleTabChange(tabName) {
  activeTab.value = tabName;

  // 如果切换到批量查询且有选中的接口，通知批量查询组件
  if (tabName === "batch" && currentInterfaceId.value && batchQueryRef.value) {
    nextTick(() => {
      if (batchQueryRef.value.onInterfaceChange) {
        batchQueryRef.value.onInterfaceChange();
      }
    });
  }
}

// ==================== 查询操作相关 ====================

/**
 * 处理查询按钮点击
 */
function handleQuery() {
  queryParams.value.pages = 1;
  handleTableDataLoad();
}

/**
 * 处理重置按钮点击
 */
function resetQuery() {
  // 重置查询参数
  Object.keys(queryParams.value).forEach((key) => {
    if (key !== "pages" && key !== "size" && key !== "interfaceId") {
      const filter = rightSideData.value.filterConfig.find(
        (f) => f.field === key
      );
      queryParams.value[key] = filter?.type === "daterange" ? [] : "";
    }
  });

  // 重置表单
  proxy.$refs.queryFormRef?.resetFields();

  // 重新查询
  queryParams.value.pages = 1;
  queryParams.value.params = {};
  handleTableDataLoad();
}

// ==================== 工具函数 ====================

// 获取表格序号
function getTableIndex(index) {
  return (queryParams.value.pages - 1) * queryParams.value.size + index + 1;
}

// 字典数据缓存
const dictCache = ref({});

// 正在加载中的字典请求标记，用于防止重复请求
const loadingDictMap = ref({});

// 获取字典选项
function getDictOptions(dictTypeId) {
  if (!dictTypeId) return [];

  // 如果缓存中已有数据，直接返回
  if (dictCache.value[dictTypeId]?.length > 0) {
    return dictCache.value[dictTypeId];
  }

  // 如果没有缓存，但已有加载中的请求，则不重复请求
  if (!loadingDictMap.value[dictTypeId]) {
    loadDictOptions(dictTypeId);
  }

  // 返回当前缓存的数据（可能为空）
  return dictCache.value[dictTypeId] || [];
}

// 异步加载字典选项
async function loadDictOptions(dictTypeId) {
  if (!dictTypeId) return;

  // 标记该字典类型正在加载中
  loadingDictMap.value[dictTypeId] = true;

  try {
    const response = await itemListOptions({ dictTypeId });
    const dictOptions = response.data || [];

    // 将获取到的数据存储到响应式缓存中
    dictCache.value[dictTypeId] = dictOptions;

    console.log(`字典数据加载成功: ${dictTypeId}`, dictOptions);
  } catch (error) {
    console.error(`获取字典数据失败: ${dictTypeId}`, error);
    // 出错时设置空数组，避免后续重复请求
    dictCache.value[dictTypeId] = [];
  } finally {
    // 无论成功失败，都标记为加载完成
    loadingDictMap.value[dictTypeId] = false;
  }
}

// 可选：清除指定字典类型的缓存
function clearDictCache(dictTypeId) {
  if (dictTypeId) {
    delete dictCache.value[dictTypeId];
  } else {
    // 不传参数则清除所有缓存
    dictCache.value = {};
  }
}

// 可选：重新加载指定字典类型的数据
function reloadDictOptions(dictTypeId) {
  if (dictTypeId) {
    clearDictCache(dictTypeId);
    loadDictOptions(dictTypeId);
  }
}

// 自定义值格式化
function formatCustomValue(row, column) {
  // 根据列配置进行自定义格式化
  if (column.formatter && typeof column.formatter === "function") {
    return column.formatter(row, column);
  }
  return row[column.field];
}

// 查看操作
async function handleView(row) {
  try {
    const response = await getSearchDetail(row.id);
    const detail = response.data;

    // 这里可以打开详情弹窗或跳转到详情页面
    console.log("查看详情:", detail);

    // 示例：跳转到详情页面
    router.push({
      path: "/query/interface/detail",
      query: { id: row.id },
    });
  } catch (error) {
    console.error("获取详情失败:", error);
    proxy.$modal.msgError("获取详情失败");
  }
}

// 编辑操作
async function handleEdit(row) {
  try {
    const response = await getSearchDetail(row.id);
    const detail = response.data;

    // 这里可以打开编辑弹窗或跳转到编辑页面
    console.log("编辑数据:", detail);

    // 示例：跳转到编辑页面
    router.push({
      path: "/query/interface/edit",
      query: { id: row.id },
    });
  } catch (error) {
    console.error("获取编辑数据失败:", error);
    proxy.$modal.msgError("获取编辑数据失败");
  }
}

// 导出操作
async function handleExport() {
  console.log(currentInterfaceData.value);
  try {
    proxy.download(
      "/interfaceQuery/exportQueryData",
      {
        ...queryParams.value,
        interfaceId: currentInterfaceId.value,
      },
      `${currentInterfaceData.value.interfaceName}_${new Date().getTime()}.xlsx`
    );
  } catch (error) {
    console.error("导出数据失败:", error);
    proxy.$modal.msgError("导出数据失败");
  }
}

// ==================== 页面初始化 ====================

/**
 * 页面初始化逻辑
 */
async function initializePage() {
  try {
    // 1. 加载级联选择器数据（接口分类）
    await loadCascaderData();

    // 2. 加载所有接口列表（不预选分类，让用户手动选择）
    await loadApiList();
  } catch (error) {
    console.error("页面初始化失败:", error);
    proxy.$modal.msgError("页面初始化失败");
  }
}

// ==================== 数据注入 ====================
// 为子组件提供数据和方法
provide("rightSideState", rightSideState);
provide("rightSideData", rightSideData);
provide("queryParams", queryParams);
provide("showActions", showActions);
provide("currentInterfaceId", currentInterfaceId);
provide("handleQuery", handleQuery);
provide("resetQuery", resetQuery);
provide("handleTableDataLoad", handleTableDataLoad);
provide("handleView", handleView);
provide("handleEdit", handleEdit);
provide("getTableIndex", getTableIndex);
provide("getDictOptions", getDictOptions);
provide("formatCustomValue", formatCustomValue);
provide("handleExport", handleExport);

// 页面挂载时初始化
onMounted(() => {
  initializePage();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .right-content {
    background: #fff;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
      padding: 0 16px;
    }

    :deep(.el-tab-pane) {
      padding: 0;
    }
  }

  .tree-container {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .tree-header {
      margin-bottom: 10px;
    }

    .tree-content {
      max-height: 600px;

      overflow-y: auto;
    }

    overflow-y: scroll;
    min-height: calc(100vh - 180px);
  }

  .query-form {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-table) {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-tree-node__content) {
    height: 36px;
    line-height: 36px;
  }

  :deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
  }

  :deep(
      .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content
    ) {
    background-color: #409eff;
    color: #fff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;

    :deep(.el-col) {
      margin-bottom: 20px;
    }

    .tree-container {
      margin-bottom: 20px;
    }

    .query-form {
      :deep(.el-form-item) {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
