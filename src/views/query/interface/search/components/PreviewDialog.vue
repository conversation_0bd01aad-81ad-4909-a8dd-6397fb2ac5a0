<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`预览`"
    width="90%"
    :fullscreen="isFullscreen"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <span :id="titleId" :class="titleClass"> 预览文件 </span>
        <div class="header-actions">
          <el-tooltip content="导出数据" placement="bottom">
            <el-button
              type="warning"
              text
              @click="handleExportData"
              :loading="exportLoading"
              class="export-btn"
            >
              <el-icon><Download /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip
            :content="isFullscreen ? '退出全屏' : '全屏显示'"
            placement="bottom"
          >
            <el-button
              type="primary"
              text
              @click="toggleFullscreen"
              class="fullscreen-btn"
            >
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <Aim v-else />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>

    <div class="preview-content v-m-20" v-loading="loading">
      <!-- 数据列表 -->
      <el-table
        :data="previewData"
        border
        stripe
        style="margin-top: 16px"
        :max-height="tableMaxHeight"
      >
        <template #empty>
          <el-empty description="暂无数据" />
        </template>

        <!-- 动态渲染表格列 -->
        <template
          v-for="(column, index) in tableColumns"
          :key="column.fieldName"
        >
          <el-table-column
            :label="column.chineseName"
            :prop="column.fieldName"
            :width="column.width || 'auto'"
            :min-width="column.minWidth || '120px'"
            :align="column.align || 'center'"
            :show-overflow-tooltip="column.showTooltip !== false"
            :fixed="index === 0 ? 'left' : false"
          >
            <template #default="scope" v-if="column.type">
              <!-- 字典标签 -->
              <template v-if="column.type === 'dict'">
                <el-tag
                  v-for="option in getDictOptions(column.dictType)"
                  :key="option.value"
                  v-show="option.value === scope.row[column.fieldName]"
                  :type="option.value === '0' ? 'success' : 'danger'"
                >
                  {{ option.label }}
                </el-tag>
              </template>

              <!-- 日期格式化 -->
              <span v-else-if="column.type === 'date'">
                {{
                  parseTime(
                    scope.row[column.fieldName],
                    column.format || "{y}-{m}-{d}"
                  )
                }}
              </span>

              <!-- 自定义格式化 -->
              <span v-else-if="column.type === 'custom'">
                {{ formatCustomValue(scope.row, column) }}
              </span>

              <!-- 默认显示 -->
              <span v-else>{{ scope.row[column.fieldName] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getPreviewData"
        style="margin-top: 16px"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, reactive, getCurrentInstance } from "vue";
import { FullScreen, Aim, Download } from "@element-plus/icons-vue";
import { parseTime } from "@/utils/ruoyi";
import { getSearchDetail, getSearchTableColumns } from "@/api/query/search";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  fileInfo: {
    type: Object,
    default: () => ({}),
  },
  interfaceId: {
    type: [String, Number],
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:modelValue"]);

const { proxy } = getCurrentInstance();

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const isFullscreen = ref(false);
const previewData = ref([]);
const tableColumns = ref([]);
const total = ref(0);

// 搜索参数
const searchParams = reactive({
  keyword: "",
});

// 查询参数
const queryParams = reactive({
  current: 1,
  size: 10,
  fileId: null,
});

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

const tableMaxHeight = computed(() => {
  return isFullscreen.value ? 600 : 400;
});

// 获取表格列配置
async function loadTableColumns() {
  if (!props.interfaceId) return;

  try {
    const response = await getSearchTableColumns({
      interfaceId: props.interfaceId,
    });

    if (response.code === 0) {
      tableColumns.value = response.data || [];
      console.log("表格列配置加载成功:", tableColumns.value);
    } else {
      console.warn("获取表格列配置失败:", response.msg);
    }
  } catch (error) {
    console.error("获取表格列配置失败:", error);
  }
}

// 获取预览数据
async function getPreviewData() {
  if (!props.fileInfo?.id) return;

  try {
    loading.value = true;

    const response = await getSearchDetail({
      ...queryParams,
      queryId: props.fileInfo.id,
      interfaceId: props.interfaceId,
    });

    if (response.code === 0) {
      previewData.value = response.data.records || [];
      total.value = response.data.total || 0;
      console.log("预览数据加载成功:", previewData.value.length, "条记录");
    } else {
      console.warn("获取预览数据失败:", response.msg);
      proxy.$modal.msgError(response.msg || "获取预览数据失败");
    }
  } catch (error) {
    proxy.$modal.msgError("获取预览数据失败");
    console.error("获取预览数据失败:", error);
  } finally {
    loading.value = false;
  }
}

// 导出数据
async function handleExportData() {
  if (!props.fileInfo?.id) {
    proxy.$modal.msgWarning("没有可导出的数据");
    return;
  }

  try {
    exportLoading.value = true;

    await proxy.download(
      "/interfaceQuery/exportBatchDetail",
      {
        queryId: props.fileInfo.id,
        interfaceId: props.interfaceId,
      },
      `${props.fileInfo.name || ""}_详情_${new Date().getTime()}.xlsx`
    );
  } catch (error) {
    console.error("导出失败:", error);
    proxy.$modal.msgError("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 获取字典选项
function getDictOptions(dictType) {
  if (!dictType) return [];

  // 这里可以从缓存中获取字典数据，或者调用API
  // 模拟字典数据
  const dictMap = {
    sys_normal_disable: [
      { label: "正常", value: "0" },
      { label: "停用", value: "1" },
    ],
    batch_query_status: [
      { label: "待处理", value: "0" },
      { label: "处理中", value: "1" },
      { label: "已完成", value: "2" },
      { label: "失败", value: "3" },
    ],
  };

  return dictMap[dictType] || [];
}

// 自定义值格式化
function formatCustomValue(row, column) {
  // 根据列配置进行自定义格式化
  if (column.formatter && typeof column.formatter === "function") {
    return column.formatter(row, column);
  }
  return row[column.fieldName];
}

// 切换全屏
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;
}

// 关闭弹框
function handleClose() {
  // 重置状态
  previewData.value = [];
  tableColumns.value = [];
  total.value = 0;
  searchParams.keyword = "";
  queryParams.current = 1;
  isFullscreen.value = false;

  emit("update:modelValue", false);
}

// 初始化数据
async function initData() {
  // 先加载列配置，再加载数据
  await loadTableColumns();
  if (props.fileInfo?.id) {
    await getPreviewData();
  }
}

// 监听弹框显示状态
watch(dialogVisible, (val) => {
  if (val) {
    initData();
  } else if (!val) {
    handleClose();
  }
});

// 监听接口ID变化，重新加载列配置
watch(
  () => props.interfaceId,
  (newId) => {
    if (newId && dialogVisible.value) {
      loadTableColumns();
    }
  }
);
</script>

<style scoped lang="scss">
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .header-actions {
    display: flex;
    gap: 8px;
    position: relative;
    top: -10px;

    .export-btn,
    .fullscreen-btn {
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f8ff;
      }

      .el-icon {
        font-size: 16px;
      }
    }

    .export-btn:hover {
      color: #e6a23c;
    }

    .fullscreen-btn:hover {
      color: #409eff;
    }
  }
}

.preview-content {
  .search-section {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
  }
}
</style>
