<template>
  <div class="single-query-container">
    <!-- 筛选表单 -->
    <div class="filter-section">
      <el-form
        ref="queryFormRef"
        :model="queryParams.params"
        inline
        class="query-form"
        v-loading="rightSideState.filterLoading"
      >
        <!-- 动态渲染筛选组件 -->
        <template
          v-for="filter in rightSideData.filterConfig"
          :key="filter.name"
        >
          <!-- 文本输入框 -->
          <el-form-item
            v-if="filter.type === 'string'"
            :label="filter.chineseName"
            :prop="filter.name"
          >
            <el-input
              v-model="queryParams.params[filter.name]"
              :placeholder="filter.placeholder || `请输入${filter.chineseName}`"
              clearable
              :style="{ width: filter.width || '200px' }"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <!-- 下拉选择框 -->
          <el-form-item
            v-else-if="filter.type === 'dictionary'"
            :label="filter.chineseName"
            :prop="filter.name"
          >
            <el-select
              v-model="queryParams.params[filter.name]"
              :placeholder="filter.placeholder || `请选择${filter.chineseName}`"
              clearable
              :style="{ width: filter.width || '200px' }"
              :multiple="filter.multiple || false"
              :loading="filter.loading || false"
            >
              <el-option
                v-for="option in getDictOptions(filter.dictTypeId)"
                :key="option.dictValue"
                :label="option.dictLabel"
                :value="option.dictValue"
              />
            </el-select>
          </el-form-item>

          <!-- 日期选择器 -->
          <el-form-item
            v-else-if="filter.type === 'date'"
            :label="filter.chineseName"
            :prop="filter.name"
          >
            <el-date-picker
              v-model="queryParams.params[filter.name]"
              :type="filter.dateType || 'date'"
              :placeholder="filter.placeholder || `请选择${filter.chineseName}`"
              clearable
              :style="{ width: filter.width || '200px' }"
              :value-format="filter.format || 'YYYY-MM-DD'"
            />
          </el-form-item>

          <!-- 日期范围选择器 -->
          <el-form-item
            v-else-if="filter.type === 'daterange'"
            :label="filter.chineseName"
            :prop="filter.name"
          >
            <el-date-picker
              v-model="queryParams.params[filter.name]"
              type="daterange"
              range-separator="-"
              :start-placeholder="filter.startPlaceholder || '开始日期'"
              :end-placeholder="filter.endPlaceholder || '结束日期'"
              clearable
              :style="{ width: filter.width || '300px' }"
              :value-format="filter.format || 'YYYY-MM-DD'"
            />
          </el-form-item>

          <!-- 数字输入框 -->
          <el-form-item
            v-else-if="filter.type === 'number'"
            :label="filter.chineseName"
            :prop="filter.name"
          >
            <el-input-number
              v-model="queryParams.params[filter.name]"
              :placeholder="filter.placeholder || `请输入${filter.chineseName}`"
              clearable
              :style="{ width: filter.width || '200px' }"
              :min="filter.min"
              :max="filter.max"
              :controls="false"
              :precision="filter.precision"
            />
          </el-form-item>
        </template>

        <!-- 查询按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport"
            >导出</el-button
          >
        </el-col>
      </el-row>
    </div>

    <!-- 动态数据表格 -->
    <el-table
      :data="rightSideData.tableData"
      v-loading="rightSideState.tableLoading"
      border
      style="margin-top: 20px"
    >
      <template #empty>
        <el-empty description="暂无数据" />
      </template>

      <!-- 序号列 -->
      <el-table-column
        label="序号"
        width="70"
        align="center"
        type="index"
        :index="getTableIndex"
      />

      <!-- 动态渲染表格列 -->
      <template
        v-for="(column, index) in rightSideData.tableColumns"
        :key="column.field"
      >
        <el-table-column
          :label="column.chineseName"
          :prop="column.fieldName"
          :width="column.width || 'auto'"
          :min-width="column.minWidth || '120px'"
          :align="column.align || 'center'"
          :show-overflow-tooltip="column.showTooltip !== false"
          :fixed="index === 0 ? 'left' : false"
        >
          <template #empty>
            <el-empty description="暂无配置记录" />
          </template>
          <template #default="scope" v-if="column.dataType">
            <!-- 字典标签 -->
            <template v-if="column.dataType === 'dictionary1'">
              <el-tag
                v-for="option in getDictOptions(column.dictTypeId)"
                :key="option.dictValue"
                v-show="option.dictValue === scope.row[column.fieldName]"
                :type="option.dictValue === '0' ? 'success' : 'danger'"
              >
                {{ option.dictLabel }}
              </el-tag>
            </template>
            <!-- 日期格式化 -->
            <span v-else-if="column.dataType === 'date'">
              {{
                parseTime(
                  scope.row[column.fieldName],
                  column.format || "{y}-{m}-{d}"
                )
              }}
            </span>
            <!-- 自定义格式化 -->
            <span v-else-if="column.dataType === 'custom'">
              {{ formatCustomValue(scope.row, column) }}
            </span>
            <!-- 默认显示 -->
            <span v-else>{{ scope.row[column.fieldName] }}</span>
          </template>
        </el-table-column>
      </template>

      <!-- 操作列 -->
      <el-table-column
        v-if="showActions"
        label="操作"
        width="150"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="handleView(row)">
            查看
          </el-button>
          <el-button type="primary" link size="small" @click="handleEdit(row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="rightSideData.total > 0"
      :total="rightSideData.total"
      v-model:page="queryParams.pages"
      v-model:limit="queryParams.size"
      @pagination="handleTableDataLoad"
      style="margin-top: 20px"
    />
  </div>
</template>

<script setup>
import { inject } from "vue";
import { parseTime } from "@/utils/ruoyi";

// 注入父组件的数据和方法
const rightSideState = inject("rightSideState");
const rightSideData = inject("rightSideData");
const queryParams = inject("queryParams");
const showActions = inject("showActions");
const handleQuery = inject("handleQuery");
const resetQuery = inject("resetQuery");
const handleTableDataLoad = inject("handleTableDataLoad");
const handleView = inject("handleView");
const handleEdit = inject("handleEdit");
const getTableIndex = inject("getTableIndex");
const getDictOptions = inject("getDictOptions");
const formatCustomValue = inject("formatCustomValue");
const handleExport = inject("handleExport");
</script>

<style scoped lang="scss">
.single-query-container {
  .filter-section {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .query-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}
</style>
