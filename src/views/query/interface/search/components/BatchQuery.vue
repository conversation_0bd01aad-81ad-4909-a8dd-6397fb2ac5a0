<template>
  <div class="batch-query-container">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="queryParams" inline class="query-form">
        <el-form-item label="用户名">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="查询时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="showImportDialog = true">
        <el-icon><Upload /></el-icon>
        导入查询模版文件
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 批量查询文件列表 -->
    <el-table
      :data="batchQueryList"
      v-loading="loading"
      border
      style="margin-top: 20px"
    >
      <template #empty>
        <el-empty description="暂无批量查询文件" />
      </template>

      <el-table-column label="序号" width="70" align="center" type="index" />

      <el-table-column prop="name" label="用户姓名"></el-table-column>

      <el-table-column prop="createTime" label="查询时间" align="center">
        <template #default="{ row }">
          {{ parseTime(row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" align="center">
        <template #default="{ row }">
          <dict-tag :options="batch_query_status" :value="row.status" />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="handleDownload(row)"
            :loading="row.downloading"
          >
            查询文件
          </el-button>
          <el-button
            type="primary"
            link
            size="small"
            @click="handlePreview(row)"
          >
            预览
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pages"
      v-model:limit="queryParams.size"
      @pagination="getBatchQueryList"
      style="margin-top: 20px"
    />

    <!-- 导入弹框 -->
    <ImportDialog
      v-model="showImportDialog"
      :interface-id="currentInterfaceId"
      :currentInterfaceData="currentInterfaceData"
      @success="handleImportSuccess"
    />

    <!-- 预览弹框 -->
    <PreviewDialog
      v-model="showPreviewDialog"
      :file-info="currentPreviewFile"
      :interface-id="currentInterfaceId"
    />
  </div>
</template>

<script setup>
import { parseTime } from "@/utils/ruoyi";
import ImportDialog from "./ImportDialog.vue";
import PreviewDialog from "./PreviewDialog.vue";
import { getSearchBatchList } from "@/api/query/search";

const { proxy } = getCurrentInstance();
const { batch_query_status } = proxy.useDict("batch_query_status");

// 注入父组件数据
const currentInterfaceId = inject("currentInterfaceId");

const props = defineProps({
  currentInterfaceData: {
    type: Object,
    default: () => {},
  },
});

// 响应式数据
const loading = ref(false);
const showImportDialog = ref(false);
const showPreviewDialog = ref(false);
const currentPreviewFile = ref(null);
const batchQueryList = ref([]);
const total = ref(0);
const dateRange = ref([]);
// 查询参数
const queryParams = reactive({
  pages: 1,
  size: 10,
  interfaceId: null,
  name: "",
});

// 获取批量查询列表
async function getBatchQueryList() {
  try {
    loading.value = true;
    queryParams.interfaceId = currentInterfaceId.value;

    // 模拟API调用
    const { data } = await getSearchBatchList(
      proxy.addDateRange(queryParams, dateRange.value, null, true)
    );
    batchQueryList.value = data.records;
    total.value = data.total;
  } catch (error) {
    proxy.$modal.msgError("获取批量查询列表失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
}

// 下载查询文件
async function handleDownload(row) {
  try {
    row.downloading = true;

    // 使用项目封装的 download 方法
    await proxy.download(
      "/oss/downloadPost",
      {
        url: row.queryFileUrl,
      },
      `${
        props.currentInterfaceData.interfaceName
      }_查询条件_${new Date().getTime()}.xlsx`
    );

    // proxy.$modal.msgSuccess("文件下载成功");
  } catch (error) {
    proxy.$modal.msgError("文件下载失败");
    console.error(error);
  } finally {
    row.downloading = false;
  }
}

// 预览文件
function handlePreview(row) {
  currentPreviewFile.value = row;
  showPreviewDialog.value = true;
}

// 搜索
function handleSearch() {
  queryParams.pages = 1;
  getBatchQueryList();
}

// 重置
function handleReset() {
  queryParams.name = "";
  dateRange.value = [];
  queryParams.pages = 1;
  getBatchQueryList();
}

// 刷新列表
function handleRefresh() {
  getBatchQueryList();
}

// 导入成功回调
function handleImportSuccess() {
  showImportDialog.value = false;
  getBatchQueryList();
}

// 监听接口变化
function onInterfaceChange() {
  if (currentInterfaceId.value) {
    getBatchQueryList();
  }
}

// 暴露给父组件的方法
defineExpose({
  onInterfaceChange,
});

onMounted(() => {
  if (currentInterfaceId.value) {
    getBatchQueryList();
  }
});
</script>

<style scoped lang="scss">
.batch-query-container {
  .filter-section {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;

    .query-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .file-name {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
