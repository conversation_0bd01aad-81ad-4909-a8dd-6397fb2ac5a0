<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入查询模版文件"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-dialog-content v-m-20">
      <!-- 模版下载区域 -->
      <div class="template-section">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon class="title-icon"><InfoFilled /></el-icon>
            导入说明
          </h3>
        </div>

        <div class="info-content">
          <div class="info-item">
            <el-icon class="step-icon"><CircleCheckFilled /></el-icon>
            <span>请先下载对应的模版文件，按照模版格式填写数据</span>
          </div>
          <div class="info-item">
            <el-icon class="step-icon"><DocumentCopy /></el-icon>
            <span>支持 .xlsx 和 .xls 格式的Excel文件</span>
          </div>
          <div class="info-item">
            <el-icon class="step-icon"><Warning /></el-icon>
            <span>单次最多支持导入1000条记录</span>
          </div>
        </div>

        <div class="template-download">
          <el-button
            type="primary"
            size="large"
            @click="downloadTemplate"
            :loading="templateLoading"
            class="download-btn"
          >
            <el-icon><Download /></el-icon>
            下载模版文件
          </el-button>
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon class="title-icon"><UploadFilled /></el-icon>
            文件上传
          </h3>
        </div>
        <fileUpload
          v-model="uploadFile"
          :fileType="['xlsx', 'xls']"
          :fileSize="10"
          :limit="1"
          :draggable="true"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="success"
          @click="handleStartImport"
          :loading="importing"
          :disabled="!uploadFile"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { batchOperation } from "@/api/query/search";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  interfaceId: {
    type: [String, Number],
    default: null,
  },
  currentInterfaceData: {
    type: Object,
    default: () => {},
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "success"]);
const { proxy } = getCurrentInstance();

// 响应式数据
const uploadFile = ref(null);
const importing = ref(false);
const templateLoading = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 下载模版文件
async function downloadTemplate() {
  try {
    templateLoading.value = true;
    proxy.download(
      "/interfaceQuery/exportQueryBatchModel",
      {
        interfaceId: props.interfaceId,
      },
      `${
        props.currentInterfaceData.interfaceName
      }_模版_${new Date().getTime()}.xlsx`
    );
  } catch (error) {
    ElMessage.error("模版文件下载失败");
    console.error(error);
  } finally {
    templateLoading.value = false;
  }
}

// 开始导入
async function handleStartImport() {
  if (!uploadFile.value) {
    ElMessage.warning("请先上传文件");
    return;
  }

  try {
    importing.value = true;

    const response = await batchOperation({
      interfaceId: props.interfaceId,
      fileUrl: uploadFile.value,
    });

    if (response.code == 0) {
      proxy.$modal.msgSuccess("正在批量查询");
      handleClose();
      emit("success");
    } else {
      proxy.$modal.msgError("批量查询失败");
    }
  } catch (error) {
    proxy.$modal.msgError("批量查询失败");
  } finally {
    importing.value = false;
  }
}

// 关闭弹框
function handleClose() {
  uploadFile.value = null;

  emit("update:modelValue", false);
}

// 监听弹框显示状态
watch(dialogVisible, (val) => {
  if (!val) {
    handleClose();
  }
});
</script>

<style scoped lang="scss">
.import-dialog-content {
  .template-section {
    .section-header {
      margin-bottom: 20px;

      .section-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        .title-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }

    .info-content {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 24px;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .step-icon {
          margin-right: 12px;
          color: #67c23a;
          font-size: 16px;
        }

        span {
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }

    .template-download {
      text-align: center;

      .download-btn {
        padding: 12px 32px;
        font-size: 14px;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

        &:hover {
          box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
          transform: translateY(-1px);
        }
      }
    }
  }

  .upload-section {
    .section-header {
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        .title-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }

    .upload-area {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 200px;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          background: #f0f8ff;
        }
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .upload-icon {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 16px;
        }

        .upload-text {
          text-align: center;

          .main-text {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: #606266;

            .click-text {
              color: #409eff;
              font-style: normal;
              font-weight: 500;
            }
          }

          .tip-text {
            margin: 0;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  .progress-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;

    .progress-text {
      text-align: center;
      margin-top: 12px;
      color: #606266;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .result-section {
    :deep(.el-alert__content) {
      p {
        margin: 4px 0;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}
</style>
