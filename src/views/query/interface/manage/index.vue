<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧API分类树 -->
      <el-col :span="5" :xs="24">
        <div class="tree-container">
          <div class="tree-header">
            <div class="tree-title">
              <span>接口目录</span>
              <el-button
                type="primary"
                size="small"
                circle
                icon="Plus"
                @click="handleAddCategory"
              />
            </div>
            <el-input
              v-model="treeFilterText"
              placeholder="输入目录名称"
              clearable
              prefix-icon="Search"
              style="margin-top: 10px"
            />
          </div>
          <div class="tree-content">
            <el-tree
              ref="treeRef"
              :data="categoryTree"
              :props="treeProps"
              :expand-on-click-node="false"
              :filter-node-method="filterTreeNode"
              node-key="id"
              highlight-current
              default-expand-all
              @node-click="handleTreeNodeClick"
              v-loading="treeLoading"
            >
              <template #default="{ node, data }">
                <div class="tree-node">
                  <el-tooltip
                    :content="node.label"
                    show-after="800"
                    placement="top"
                  >
                    <span class="tree-node-label v-line-1">{{
                      node.label
                    }}</span>
                  </el-tooltip>
                  <div class="tree-node-actions">
                    <el-button
                      type="warning"
                      size="small"
                      text
                      icon="Edit"
                      @click.stop="handleEditCategory(data)"
                    />
                    <el-button
                      type="success"
                      size="small"
                      text
                      icon="Plus"
                      @click.stop="handleAddCategory(data)"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      text
                      icon="Delete"
                      @click.stop="handleDeleteCategory(data)"
                    />
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <!-- 右侧API接口列表 -->
      <el-col :span="19" :xs="24">
        <!-- 筛选区域 -->
        <el-form
          ref="queryFormRef"
          :model="queryParams"
          inline
          class="query-form"
        >
          <el-form-item>
            <el-input
              v-model="queryParams.name"
              placeholder="输入API名称"
              clearable
              prefix-icon="Search"
              style="width: 300px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">
              查询
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="Plus" @click="handleAdd">
                新增
              </el-button>
            </el-col>
          </el-row>
        </el-form>

        <!-- API接口表格 -->
        <el-table
          :data="apiList"
          v-loading="tableLoading"
          border
          style="margin-top: 20px"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty description="暂无接口数据" />
          </template>

          <el-table-column type="selection" width="55" align="center" />

          <el-table-column
            label="序号"
            width="70"
            align="center"
            type="index"
            fixed="left"
            :index="getTableIndex"
          />

          <el-table-column
            label="接口名称"
            prop="interfaceName"
            fixed="left"
            min-width="150"
            show-overflow-tooltip
          />

          <el-table-column
            label="接口类型"
            prop="type"
            width="100"
            align="center"
          >
            <template #default="scope">
              <dict-tag
                :options="interface_type"
                :value="scope.row.interfaceType"
              />
            </template>
          </el-table-column>

          <el-table-column
            label="接口目录"
            prop="interfaceCata"
            width="120"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column
            label="接口描述"
            prop="description"
            min-width="200"
            show-overflow-tooltip
          />

          <el-table-column
            label="创建人"
            prop="createBy"
            width="100"
            align="center"
          />

          <el-table-column
            label="创建时间"
            prop="createTime"
            width="180"
            align="center"
          >
            <template #default="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="198"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="View"
                @click="handleView(scope.row)"
              >
                详情
              </el-button>
              <el-button
                link
                type="success"
                icon="Edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getApiList"
          style="margin-top: 20px"
        />
      </el-col>
    </el-row>

    <!-- API分类管理弹框 -->
    <CategoryManage
      v-model="categoryDialogVisible"
      :edit-data="currentCategory"
      :parent-data="currentCategoryParentData"
      @success="handleCategorySuccess"
      ref="categoryManageRef"
    />

    <!-- API接口新增/编辑弹框 -->
    <ApiForm
      v-model="apiDialogVisible"
      :edit-data="currentApi"
      :category-options="categoryTree"
      @success="handleApiSuccess"
    />

    <!-- API详情弹框 -->
    <BaseDialog
      v-model="detailDialogVisible"
      title="接口详情"
      width="800px"
      destroy-on-close
    >
      <div v-if="apiDetail" class="api-detail">
        <div class="v-title v-m-b-20">基本信息</div>
        <el-descriptions label-width="120" :column="2" border>
          <el-descriptions-item label="接口名称">
            {{ apiDetail.interfaceName }}
          </el-descriptions-item>
          <el-descriptions-item label="接口标识">
            {{ apiDetail.interfaceCode }}
          </el-descriptions-item>
          <el-descriptions-item label="接口来源">
            {{ apiDetail.source }}
          </el-descriptions-item>
          <el-descriptions-item label="接口目录">
            {{ apiDetail.interfaceCataName }}
          </el-descriptions-item>
          <el-descriptions-item label="接口类型">
            <dict-tag
              :options="interface_type"
              :value="apiDetail.interfaceType"
            />
          </el-descriptions-item>
          <el-descriptions-item label="接口地址">
            {{ apiDetail.requestUrl }}
          </el-descriptions-item>

          <el-descriptions-item label="请求方式">
            <dict-tag
              :options="request_method"
              :value="apiDetail.interfaceType"
            />
          </el-descriptions-item>
          <el-descriptions-item label="限制调用次数">
            {{ apiDetail.callCount }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ apiDetail.creator }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ parseTime(apiDetail.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </el-descriptions-item>
          <el-descriptions-item label="接口描述" :span="2">
            {{ apiDetail.description || "暂无描述" }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- <div class="v-title v-m-y-20">权限配置</div>
        <el-descriptions label-width="120" :column="2" border>
          <el-descriptions-item label="认证类型">
            {{ apiDetail.authType }}
          </el-descriptions-item>
          <el-descriptions-item label="加密凭证" :span="2">
            <el-input
              :value="apiDetail.encryptionCredential"
              type="textarea"
              :rows="3"
              readonly
            />
          </el-descriptions-item>
        </el-descriptions> -->
        <div class="v-title v-m-y-20">令牌信息</div>
        <el-descriptions label-width="120" :column="2" border>
          <el-descriptions-item label="令牌类型">
            {{ apiDetail.tokenType }}
          </el-descriptions-item>
          <el-descriptions-item label="令牌url" :span="2">
            <el-input
              :value="apiDetail.tokenUrl"
              type="textarea"
              :rows="3"
              readonly
            />
          </el-descriptions-item>
          <el-descriptions-item label="客户端ID">
            {{ apiDetail.clientId }}
          </el-descriptions-item>
          <el-descriptions-item label="客户端key">
            {{ apiDetail.clientKey }}
          </el-descriptions-item>
          <el-descriptions-item label="客户端秘钥">
            {{ apiDetail.clientSecret }}
          </el-descriptions-item>
          <el-descriptions-item label="令牌参数(JSON格式)" :span="2">
            <el-input
              :value="apiDetail.tokenParams"
              type="textarea"
              :rows="5"
              readonly
            />
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </BaseDialog>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watch } from "vue";
import { parseTime } from "@/utils/ruoyi";
import {
  deleteApiCategory,
  getApiCategoryTree,
  getApiInterfaceList,
  getApiInterface,
  deleteApiInterface,
} from "@/api/query/interface";
import CategoryManage from "./components/CategoryManage.vue";
import ApiForm from "./components/ApiForm.vue";

const { proxy } = getCurrentInstance();
const { request_method, interface_type } = proxy.useDict(
  "request_method",
  "interface_type"
);

// 响应式数据
const treeLoading = ref(false);
const tableLoading = ref(false);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);

// 左侧树形数据
const treeFilterText = ref("");
const categoryTree = ref([]);
const treeProps = {
  label: "cateName",
  children: "children",
  value: "id",
};

// 查询参数
const queryParams = ref({
  current: 1,
  size: 10,
  name: "",
  interfaceCata: null,
});

// API列表数据
const apiList = ref([]);

// 弹框控制
const categoryDialogVisible = ref(false);
const apiDialogVisible = ref(false);
const detailDialogVisible = ref(false);

// 当前操作的数据
const currentCategory = ref(null);
const currentCategoryParentData = ref(null);
const currentApi = ref(null);
const apiDetail = ref(null);

// 树形节点过滤
const filterTreeNode = (value, data) => {
  if (!value) return true;
  return data.cateName.indexOf(value) !== -1;
};

// 监听树形搜索
watch(treeFilterText, (val) => {
  proxy.$refs.treeRef?.filter(val);
});

// 获取API分类树
async function getCategoryTree() {
  try {
    treeLoading.value = true;
    const response = await getApiCategoryTree();
    categoryTree.value = response.data || [];
  } catch (error) {
    console.error("获取分类树失败:", error);
    proxy.$modal.msgError("获取分类树失败");
  } finally {
    treeLoading.value = false;
  }
}

// 获取API接口列表
async function getApiList() {
  try {
    tableLoading.value = true;
    const response = await getApiInterfaceList(queryParams.value);
    apiList.value = response.data.records || [];
    total.value = response.data.total || 0;
  } catch (error) {
    console.error("获取API列表失败:", error);
    proxy.$modal.msgError("获取API列表失败");
  } finally {
    tableLoading.value = false;
  }
}

// 树节点点击事件
function handleTreeNodeClick(data) {
  queryParams.value.interfaceCata = data.id;
  queryParams.value.current = 1;
  getApiList();
}

// 查询按钮
function handleQuery() {
  queryParams.value.current = 1;
  getApiList();
}

// 重置按钮
function resetQuery() {
  queryParams.value.name = "";
  queryParams.value.interfaceCata = null;
  queryParams.value.current = 1;

  proxy.$refs.queryFormRef?.resetFields();
  getApiList();
}

// 获取表格序号
function getTableIndex(index) {
  return (queryParams.value.current - 1) * queryParams.value.size + index + 1;
}

// 选择条数
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// 新增API分类
function handleAddCategory(data = {}) {
  currentCategory.value = null;
  currentCategoryParentData.value = data;
  categoryDialogVisible.value = true;
}

// 编辑API分类
function handleEditCategory(data) {
  currentCategory.value = { ...data };
  categoryDialogVisible.value = true;
}

// 删除API分类
function handleDeleteCategory(row) {
  const id = row.id;
  proxy.$modal
    .confirm('是否确认删除"' + row.cateName + '"的数据项?', "温馨提示")
    .then(() => {
      treeLoading.value = true;
      return deleteApiCategory(id);
    })
    .then(() => {
      treeLoading.value = false;
      proxy.$modal.msgSuccess("删除成功");
      getCategoryTree();
    })
    .finally(() => {
      treeLoading.value = false;
    });
}

// 分类操作成功回调
function handleCategorySuccess() {
  getCategoryTree();
}
// 新增API接口
function handleAdd() {
  currentApi.value = null;
  apiDialogVisible.value = true;
}

// 编辑API接口
function handleEdit(row) {
  currentApi.value = { ...row };
  apiDialogVisible.value = true;
}

// 查看API详情
async function handleView(row) {
  try {
    const response = await getApiInterface(row.id);
    apiDetail.value = response.data;
    detailDialogVisible.value = true;
  } catch (error) {
    console.error("获取API详情失败:", error);
    proxy.$modal.msgError("获取API详情失败");
  }
}

// 删除API接口
async function handleDelete(row) {
  try {
    const apiIds = row.id ? row.id : ids.value?.join(",");
    const names = row.interfaceName
      ? row.interfaceName
      : apiList.value
          .filter((item) => apiIds.includes(item.id))
          .map((item) => item.name)
          .join("、");

    await proxy.$modal.confirm(`确定要删除API接口"${names}"吗？`);

    await deleteApiInterface({
      id: apiIds,
    });
    proxy.$modal.msgSuccess("删除成功");
    await getApiList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      proxy.$modal.msgError("删除失败");
    }
  }
}

// API操作成功回调
function handleApiSuccess() {
  getApiList();
}

// 页面初始化
onMounted(async () => {
  await getCategoryTree();
  await getApiList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .tree-container {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 170px);
    overflow-y: scroll;

    .tree-header {
      margin-bottom: 15px;

      .tree-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        color: #303133;
        margin-bottom: 10px;
      }
    }

    .tree-content {
      max-height: 600px;
      overflow-y: auto;

      .tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .tree-node-label {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 130px;
        }

        .tree-node-actions {
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            padding: 2px;
            margin-left: 2px;
          }
        }

        &:hover .tree-node-actions {
          opacity: 1;
        }
      }
    }
  }

  .query-form {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-table) {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-tree-node__content) {
    height: 36px;
    line-height: 36px;
  }

  :deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
  }

  :deep(
      .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content
    ) {
    background-color: #409eff;
    color: #fff;
  }

  .api-detail {
    .el-descriptions {
      margin-bottom: 20px;
    }

    .el-divider {
      margin: 20px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;

    :deep(.el-col) {
      margin-bottom: 20px;
    }

    .tree-container {
      margin-bottom: 20px;
    }

    .query-form {
      :deep(.el-form-item) {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.tree-node {
  position: relative;
  .tree-node-actions {
    position: absolute;
    right: 5px;
  }
}

:deep(.el-button + .el-button) {
  margin-left: 5px;
}
</style>
