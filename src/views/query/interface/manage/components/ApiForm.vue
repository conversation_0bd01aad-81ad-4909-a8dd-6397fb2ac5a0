<template>
  <el-dialog
    :title="isEdit ? '编辑API' : '新增API'"
    v-model="visible"
    width="800px"
    append-to-body
    @close="handleClose"
  >
    <!-- 步骤条 -->
    <el-steps :active="currentStep" align-center style="margin-bottom: 30px">
      <el-step title="基本信息" />
      <el-step title="鉴权信息" />
    </el-steps>

    <!-- 基本信息步骤 -->
    <div v-show="currentStep === 0">
      <el-form
        ref="basicFormRef"
        :model="basicForm"
        :rules="basicRules"
        label-width="120px"
      >
        <el-form-item label="接口名称" prop="interfaceName">
          <el-input
            v-model="basicForm.interfaceName"
            placeholder="请输入接口名称"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="接口标识" prop="interfaceCode">
          <el-input
            v-model="basicForm.interfaceCode"
            placeholder="请输入接口标识"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="接口来源" prop="source">
          <el-input
            v-model="basicForm.source"
            placeholder="请输入接口来源"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="接口目录" prop="interfaceCata">
          <el-tree-select
            v-model="basicForm.interfaceCata"
            :data="categoryOptions"
            :props="{ value: 'id', label: 'cateName', children: 'children' }"
            placeholder="请选择接口目录"
            style="width: 100%"
            check-strictly
          />
        </el-form-item>

        <el-form-item label="接口类型" prop="interfaceType">
          <el-select
            v-model="basicForm.interfaceType"
            placeholder="请选择接口类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in interface_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="接口地址" prop="requestUrl">
          <el-input
            v-model="basicForm.requestUrl"
            placeholder="请输入接口地址"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="请求方式" prop="requestMethod">
          <el-select
            v-model="basicForm.requestMethod"
            placeholder="请选择请求方式"
            style="width: 100%"
          >
            <el-option
              v-for="dict in request_method"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="限制调用次数" prop="callCount">
          <el-input-number
            v-model="basicForm.callCount"
            :min="0"
            :controls="false"
            placeholder="请输入限制调用次数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="接口描述" prop="description">
          <el-input
            v-model="basicForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入接口描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 获取令牌信息步骤 -->
    <div v-show="currentStep === 1">
      <!-- 大数据局令牌信息 -->
      <div class="section-title">
        <span class="title-icon">🔵</span>
        令牌信息
      </div>

      <el-form
        ref="tokenFormRef"
        :model="tokenForm"
        :rules="tokenRules"
        label-width="120px"
      >
        <el-form-item label="令牌类型" prop="tokenType">
          <el-select
            v-model="tokenForm.tokenType"
            placeholder="令牌类型一"
            style="width: 100%"
          >
            <el-option
              v-for="dict in token_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="令牌url" prop="tokenUrl">
          <el-input
            v-model="tokenForm.tokenUrl"
            type="textarea"
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label="客户端ID" prop="clientId">
          <el-input v-model="tokenForm.clientId" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="客户端key" prop="clientKey">
          <el-input v-model="tokenForm.clientKey" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="客户端秘钥" prop="clientSecret">
          <el-input v-model="tokenForm.clientSecret" placeholder="请输入" />
        </el-form-item>
      </el-form>

      <!-- 其他令牌信息 -->
      <div class="section-title">
        <span class="title-icon">🔵</span>
        其他令牌信息
      </div>

      <el-form
        ref="otherTokenFormRef"
        :model="tokenForm"
        :rules="tokenRules"
        label-width="120px"
      >
        <el-form-item label="令牌参数(JSON格式)" prop="tokenParams">
          <el-input
            v-model="tokenForm.tokenParams"
            type="textarea"
            :rows="5"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="currentStep < 1"
          type="primary"
          :loading="loading"
          @click="nextStep"
        >
          下一步
        </el-button>
        <el-button
          v-else
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from "vue";
import {
  addApiInterfaceBasic,
  updateApiInterfaceBasic,
  addApiInterfaceDataSource,
  updateApiInterfaceDataSource,
  getApiInterface,
} from "@/api/query/interface";

const { proxy } = getCurrentInstance();
const { interface_type, request_method, token_type } = proxy.useDict(
  "interface_type",
  "request_method",
  "token_type"
);

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: null,
  },
  categoryOptions: {
    type: Array,
    default: () => [],
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue", "success"]);

// 响应式数据
const visible = ref(false);
const loading = ref(false);

const currentStep = ref(0);
const isEdit = ref(false);
const apiId = ref(null);
const detailData = ref({});

// 基本信息表单
const basicForm = reactive({
  interfaceName: "",
  interfaceCode: "",
  source: "",
  interfaceType: "",
  interfaceCata: "",
  description: "",
  requestUrl: "",
  requestMethod: "",
  callCount: "",
  id: "",
});

// 令牌信息表单
const tokenForm = reactive({
  tokenType: "",
  tokenUrl: "",
  clientId: "",
  clientKey: "",
  clientSecret: "",
  tokenParams: "",
  // interfaceTokenId: "",
});

// 表单验证规则
const basicRules = {
  interfaceName: [
    { required: true, message: "请输入接口名称", trigger: "blur" },
  ],
  interfaceCode: [
    { required: true, message: "请输入接口标识", trigger: "blur" },
  ],
  interfaceType: [
    { required: true, message: "请选择接口类型", trigger: "change" },
  ],
  requestMethod: [
    { required: true, message: "请选择请求方式", trigger: "change" },
  ],
  requestUrl: [{ required: true, message: "请输入接口地址", trigger: "blur" }],
  callCount: [
    { required: true, message: "请输入限制调用次数", trigger: "blur" },
  ],
};

const tokenRules = {
  tokenType: [{ required: true, message: "请选择令牌类型", trigger: "change" }],
};

// 监听显示状态
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val) {
      if (props.editData) {
        isEdit.value = true;
        apiId.value = props.editData.id;
        loadEditData();
      } else {
        isEdit.value = false;
        apiId.value = null;
        resetForms();
      }
    }
  }
);

function assignExistingProperties(target, source) {
  // 只复制目标对象自身已有的属性
  Object.keys(target).forEach((key) => {
    if (source.hasOwnProperty(key)) {
      target[key] = source[key];
    }
  });
}

// 加载编辑数据
function loadEditData() {
  // 这里应该从API获取完整的编辑数据
  // 暂时使用传入的数据

  getApiInterface(props.editData.id).then((response) => {
    const data = response.data;
    assignExistingProperties(basicForm, data);
    assignExistingProperties(tokenForm, data);
    detailData.value = data;
  });
}

// 重置表单
function resetForms() {
  currentStep.value = 0;
  Object.assign(basicForm, {
    interfaceName: "",
    interfaceCode: "",
    source: "",
    interfaceType: "",
    interfaceCata: "",
    description: "",
    requestUrl: "",
    requestMethod: "",
    callCount: "",
    id: "",
  });
  Object.assign(tokenForm, {
    tokenType: "",
    tokenUrl: "",
    clientId: "",
    clientKey: "",
    clientSecret: "",
    tokenParams: "",
    interfaceTokenId: "",
  });
}

// 关闭弹框
function handleClose() {
  emit("update:modelValue", false);
  resetForms();
}

// 下一步
async function nextStep() {
  try {
    if (currentStep.value === 0) {
      await proxy.$refs.basicFormRef.validate();
      await saveBasicInfo();
      currentStep.value++;
    }
  } catch (error) {
    console.error("验证失败:", error);
  }
}

// 上一步
function prevStep() {
  currentStep.value--;
}

// 保存基本信息
async function saveBasicInfo() {
  try {
    loading.value = true;
    const data = { ...basicForm };
    if (apiId.value) {
      data.id = apiId.value;
    }

    if (isEdit.value) {
      await updateApiInterfaceBasic(data);
    } else {
      const response = await addApiInterfaceBasic(data);
      apiId.value = response.data.id;
    }
  } catch (error) {
    console.error("保存基本信息失败:", error);
    throw error;
  } finally {
    loading.value = false;
  }
}

// 保存令牌信息
async function saveTokenInfo() {
  try {
    loading.value = true;
    const data = { ...tokenForm, interfaceId: apiId.value };

    if (isEdit.value) {
      data.id = detailData.value?.interfaceTokenId;
      await updateApiInterfaceDataSource(data);
    } else {
      await addApiInterfaceDataSource(data);
    }
  } catch (error) {
    console.error("保存令牌信息失败:", error);
    throw error;
  } finally {
    loading.value = false;
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await proxy.$refs.tokenFormRef.validate();
    await saveTokenInfo();

    proxy.$modal.msgSuccess(isEdit.value ? "修改成功" : "新增成功");
    emit("success");
    handleClose();
  } catch (error) {
    console.error("提交失败:", error);
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: center;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;

  .title-icon {
    margin-right: 8px;
    font-size: 14px;
  }
}
</style>
