<template>
  <el-dialog
    title="新增目录"
    v-model="visible"
    width="500px"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item
        v-if="parentData?.id"
        label="父级目录"
        prop="parentCateName"
      >
        <el-input v-model="parentData.cateName" disabled maxlength="50" />
      </el-form-item>

      <el-form-item label="目录名称" prop="cateName">
        <el-input
          v-model="form.cateName"
          placeholder="请输入目录名称"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          :min="0"
          :controls="false"
          placeholder="请输入排序"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 删除确认弹框 -->
  <el-dialog title="删除" v-model="deleteVisible" width="400px" append-to-body>
    <div style="text-align: center; padding: 20px 0">
      <!-- <el-icon style="font-size: 48px; color: #f56c6c; margin-bottom: 16px">
        <WarningFilled />
      </el-icon> -->
      <p style="margin: 0; font-size: 16px">
        确认删除 <strong>{{ deleteItem?.cateName }}</strong> 吗？
      </p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button
          type="danger"
          :loading="deleteLoading"
          @click="confirmDelete"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 删除确认弹框 -->
  <el-dialog
    title="删除确认"
    v-model="deleteConfirmVisible"
    width="400px"
    append-to-body
  >
    <div style="text-align: center; padding: 20px 0">
      <p style="margin: 0; font-size: 16px">
        {{ deleteItem?.cateName }} 目录下有接口数据，是否确认删除？
      </p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteConfirmVisible = false">取消</el-button>
        <el-button type="danger" :loading="deleteLoading" @click="forceDelete">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from "vue";
import { WarningFilled } from "@element-plus/icons-vue";
import {
  addApiCategory,
  updateApiCategory,
  deleteApiCategory,
} from "@/api/query/interface";

const { proxy } = getCurrentInstance();

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: null,
  },
  parentData: {
    type: Object,
    default: () => {},
  },
});

// 组件事件
const emit = defineEmits(["update:modelValue", "success"]);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const deleteVisible = ref(false);
const deleteConfirmVisible = ref(false);
const deleteLoading = ref(false);
const deleteItem = ref(null);

// 表单数据
const form = reactive({
  id: null,
  cateName: "",
  sort: 0,
  remark: "",
});

// 表单验证规则
const rules = {
  cateName: [
    { required: true, message: "请输入目录名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  sort: [{ required: true, message: "请输入排序", trigger: "blur" }],
};

// 监听显示状态
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val && props.editData) {
      Object.assign(form, props.editData);
    } else if (val) {
      resetForm();
    }
  }
);

// 重置表单
function resetForm() {
  Object.assign(form, {
    id: null,
    cateName: "",
    sort: 0,
    remark: "",
  });
  proxy.$refs.formRef?.resetFields();
}

// 关闭弹框
function handleClose() {
  emit("update:modelValue", false);
  resetForm();
}

// 提交表单
async function handleSubmit() {
  try {
    await proxy.$refs.formRef.validate();
    loading.value = true;

    if (form.id) {
      await updateApiCategory(form);
      proxy.$modal.msgSuccess("修改成功");
    } else {
      let params = {
        cateName: form.cateName,
        sort: form.sort,
        remark: form.remark,
      };
      params.parentId = props?.parentData?.id;
      await addApiCategory(params);
      proxy.$modal.msgSuccess("新增成功");
    }

    emit("success");
    handleClose();
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    loading.value = false;
  }
}

// 显示删除确认
function showDeleteConfirm(item) {
  deleteItem.value = item;
  deleteVisible.value = true;
}

// 确认删除
async function confirmDelete() {
  try {
    deleteLoading.value = true;
    await deleteApiCategory(deleteItem.value.id);
    proxy.$modal.msgSuccess("删除成功");
    deleteVisible.value = false;
    emit("success");
  } catch (error) {
    if (error.code === "HAS_CHILDREN") {
      deleteVisible.value = false;
      deleteConfirmVisible.value = true;
    } else {
      console.error("删除失败:", error);
      proxy.$modal.msgError("删除失败");
    }
  } finally {
    deleteLoading.value = false;
  }
}

// 强制删除
async function forceDelete() {
  try {
    deleteLoading.value = true;
    await deleteApiCategory(deleteItem.value.id, { force: true });
    proxy.$modal.msgSuccess("删除成功");
    deleteConfirmVisible.value = false;
    emit("success");
  } catch (error) {
    console.error("删除失败:", error);
    proxy.$modal.msgError("删除失败");
  } finally {
    deleteLoading.value = false;
  }
}

// 暴露方法
defineExpose({
  showDeleteConfirm,
});
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: center;
}
:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
