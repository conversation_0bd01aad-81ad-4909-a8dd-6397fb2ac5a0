<template>
  <div class="login">
    <div class="login-header">
      <img src="../assets/images/national.png" />
      <p>{{ systemTitle }}</p>
    </div>
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <h3 class="title">{{ systemTitle }}</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="请输入用户名"
        >
          <template #prefix>
            <img src="../assets/images/user.png" class="input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="请输入密码"
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <img src="../assets/images/password.png" class="input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          placeholder="请输入验证码"
          style="width: 63%"
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <img src="../assets/images/validCode.png" class="input-icon" />
          </template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <!-- <el-checkbox
        v-model="loginForm.rememberMe"
        style="margin: 0px 0px 25px 0px"
        >记住密码</el-checkbox
      > -->
      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width: 100%"
          @click.prevent="handleLogin"
          class="login-btn"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right" v-if="register">
          <router-link class="link-type" :to="'/register'"
            >立即注册</router-link
          >
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span>Copyright © 2018-2025 Li All Rights Reserved.</span>
    </div> -->
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
// import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";
import usePermissionStore from "@/store/modules/permission";
const userStore = useUserStore();

const router = useRouter();
const { proxy } = getCurrentInstance();
import { nextTick } from "vue";
const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const systemTitle = ref(import.meta.env.VITE_APP_TITLE);

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

function handleLogin() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      // if (loginForm.value.rememberMe) {
      //   Cookies.set("username", loginForm.value.username, { expires: 30 });
      //   Cookies.set("password", encrypt(loginForm.value.password), {
      //     expires: 30,
      //   });
      //   Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      // } else {
      //   // 否则移除
      //   Cookies.remove("username");
      //   Cookies.remove("password");
      //   Cookies.remove("rememberMe");
      // }
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(() => {
          useUserStore()
            .getInfo()
            .then(() => {
              usePermissionStore()
                .generateRoutes()
                .then((accessRoutes) => {
                  const firstVisibleRoute = accessRoutes.find(
                    (route) => !route.hidden
                  );

                  if (firstVisibleRoute) {
                    let path = firstVisibleRoute.path;

                    const firstChild = firstVisibleRoute.children?.find(
                      (child) => !child.hidden
                    );
                    if (firstChild) {
                      const parentPath = path.replace(/\/+$/, ""); // 去除末尾斜杠
                      const childPath = firstChild.path.replace(/^\/+/, ""); // 去除开头斜杠
                      path = `${parentPath}/${childPath}`;
                    }

                    // 使用 nextTick 确保路由已更新
                    nextTick().then(() => {
                      console.log("firstVisibleRoute:", path);
                      location.href =
                        window.location.origin +
                        import.meta.env.VITE_APP_CONTEXT_PATH +
                        path;
                    });
                  } else {
                    router.push("/");
                  }
                });
            });
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode();
          }
        });
    }
  });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.data.img;
      loginForm.value.uuid = res.data.uuid;
    }
  });
}

// function getCookie() {
//   const username = Cookies.get("username");
//   const password = Cookies.get("password");
//   const rememberMe = Cookies.get("rememberMe");
//   loginForm.value = {
//     username: username === undefined ? loginForm.value.username : username,
//     password:
//       password === undefined ? loginForm.value.password : decrypt(password),
//     rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
//   };
// }

getCode();
// getCookie();
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("@/assets/images/login-background.png");
  background-size: 100% 100%;
  position: relative;
  .login-header {
    position: absolute;
    top: 8px;
    left: 23px;
    display: flex;
    align-items: center;
    img {
      width: 35px;
      height: 35px;
      margin-right: 13px;
    }
    > p {
      font-size: 21px;
      color: #fff;
      font-weight: 600;
      letter-spacing: 2px;
    }
  }
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #1a1a1a;
  font-weight: 700;
  font-size: 27px;
  letter-spacing: 2px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 440px;
  padding: 32px 40px;
  box-shadow: 0px 7px 29px 0px rgba(5, 81, 151, 0.08);
  border-radius: 16px;
  .el-input {
    height: 48px;
    input {
      height: 48px;
    }
  }
  .input-icon {
    height: 20px;
    width: 20px;
    margin-left: 0px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 46px;
  padding-left: 12px;
}
.el-button.login-btn {
  height: 54px;
  background-color: #005dba;
  font-size: 18px;
  font-weight: 500;
  margin-top: 20px;
}
</style>
