<template>
  <div class="example-container">
    <h2>BaseDialog 组件使用示例</h2>
    
    <!-- 示例按钮 -->
    <div class="button-group">
      <el-button type="primary" @click="showBasicDialog">基础弹框</el-button>
      <el-button type="success" @click="showCustomHeaderDialog">自定义头部</el-button>
      <el-button type="warning" @click="showNoFullscreenDialog">无全屏按钮</el-button>
      <el-button type="info" @click="showLargeDialog">大尺寸弹框</el-button>
    </div>

    <!-- 基础弹框示例 -->
    <BaseDialog
      v-model="basicDialogVisible"
      title="基础弹框示例"
      width="600px"
      destroy-on-close
    >
      <div class="dialog-content">
        <p>这是一个基础的弹框示例，包含全屏功能。</p>
        <el-form :model="form" label-width="100px">
          <el-form-item label="用户名">
            <el-input v-model="form.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="basicDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </BaseDialog>

    <!-- 自定义头部弹框示例 -->
    <BaseDialog
      v-model="customHeaderDialogVisible"
      width="700px"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-header">
          <el-icon class="header-icon"><User /></el-icon>
          <span :id="titleId" :class="titleClass">用户管理</span>
          <el-tag type="success" size="small">新功能</el-tag>
        </div>
      </template>
      
      <template #header-actions>
        <el-button type="primary" text size="small">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </template>

      <div class="dialog-content">
        <p>这是一个自定义头部的弹框示例。</p>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="name" label="姓名" width="180" />
          <el-table-column prop="email" label="邮箱" width="200" />
          <el-table-column prop="role" label="角色" />
        </el-table>
      </div>
      
      <template #footer>
        <el-button @click="customHeaderDialogVisible = false">关闭</el-button>
      </template>
    </BaseDialog>

    <!-- 无全屏按钮弹框示例 -->
    <BaseDialog
      v-model="noFullscreenDialogVisible"
      title="无全屏按钮弹框"
      :show-fullscreen="false"
      width="500px"
    >
      <div class="dialog-content">
        <p>这个弹框没有全屏按钮。</p>
        <el-alert
          title="提示信息"
          type="info"
          description="这是一个简单的提示弹框，不需要全屏功能。"
          show-icon
        />
      </div>
      
      <template #footer>
        <el-button type="primary" @click="noFullscreenDialogVisible = false">
          知道了
        </el-button>
      </template>
    </BaseDialog>

    <!-- 大尺寸弹框示例 -->
    <BaseDialog
      v-model="largeDialogVisible"
      title="大尺寸弹框示例"
      width="90%"
      dialog-class="large-dialog"
    >
      <div class="dialog-content">
        <p>这是一个大尺寸的弹框，适合展示复杂内容。</p>
        <div class="content-grid">
          <div class="grid-item" v-for="i in 12" :key="i">
            <el-card>
              <h4>卡片 {{ i }}</h4>
              <p>这是卡片内容 {{ i }}</p>
            </el-card>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="largeDialogVisible = false">关闭</el-button>
        <el-button type="primary">保存</el-button>
      </template>
    </BaseDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { User, Refresh } from '@element-plus/icons-vue'

// 弹框显示状态
const basicDialogVisible = ref(false)
const customHeaderDialogVisible = ref(false)
const noFullscreenDialogVisible = ref(false)
const largeDialogVisible = ref(false)

// 表单数据
const form = ref({
  username: '',
  email: '',
  description: ''
})

// 表格数据
const tableData = ref([
  { name: '张三', email: '<EMAIL>', role: '管理员' },
  { name: '李四', email: '<EMAIL>', role: '用户' },
  { name: '王五', email: '<EMAIL>', role: '编辑' }
])

// 方法
const showBasicDialog = () => {
  basicDialogVisible.value = true
}

const showCustomHeaderDialog = () => {
  customHeaderDialogVisible.value = true
}

const showNoFullscreenDialog = () => {
  noFullscreenDialogVisible.value = true
}

const showLargeDialog = () => {
  largeDialogVisible.value = true
}

const handleSubmit = () => {
  console.log('提交表单:', form.value)
  basicDialogVisible.value = false
}
</script>

<style scoped lang="scss">
.example-container {
  padding: 20px;
}

.button-group {
  margin-bottom: 20px;
  
  .el-button {
    margin-right: 10px;
  }
}

.dialog-content {
  padding: 10px 0;
}

.custom-header {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .header-icon {
    color: #409eff;
    font-size: 18px;
  }
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  
  .grid-item {
    .el-card {
      height: 120px;
    }
  }
}

:deep(.large-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}
</style>
