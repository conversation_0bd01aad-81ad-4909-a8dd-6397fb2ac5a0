<template>
  <el-dialog
    v-model="dialogVisible"
    :width="computedWidth"
    :fullscreen="isFullscreen"
    :before-close="handleClose"
    :destroy-on-close="destroyOnClose"
    :class="['base-dialog', dialogClass]"
    v-bind="$attrs"
    @opened="handleOpened"
    @closed="handleClosed"
  >
    <!-- 自定义头部 -->
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <!-- 使用插槽或默认标题 -->
        <slot
          name="header"
          :close="close"
          :titleId="titleId"
          :titleClass="titleClass"
        >
          <span :id="titleId" :class="titleClass">{{ title }}</span>
        </slot>

        <!-- 头部操作按钮区域 -->
        <div class="header-actions">
          <!-- 自定义头部操作按钮插槽 -->
          <slot name="header-actions"></slot>

          <!-- 全屏切换按钮 -->
          <el-tooltip
            v-if="showFullscreen"
            :content="isFullscreen ? '退出全屏' : '全屏显示'"
            placement="bottom"
          >
            <el-button
              type="primary"
              text
              @click="toggleFullscreen"
              class="fullscreen-btn"
            >
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <Aim v-else />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>

    <!-- 弹框内容 -->
    <slot></slot>

    <!-- 底部操作区域 -->
    <template #footer v-if="$slots.footer">
      <slot name="footer"></slot>
    </template>
  </el-dialog>
</template>

<script setup name="BaseDialog">
import { ref, computed, watch } from "vue";
import { FullScreen, Aim } from "@element-plus/icons-vue";

// Props 定义
const props = defineProps({
  // 控制弹框显示/隐藏
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 弹框标题
  title: {
    type: String,
    default: "",
  },
  // 弹框宽度
  width: {
    type: [String, Number],
    default: "50%",
  },
  // 是否显示全屏按钮
  showFullscreen: {
    type: Boolean,
    default: true,
  },
  // 是否在关闭时销毁
  destroyOnClose: {
    type: Boolean,
    default: false,
  },
  // 自定义弹框类名
  dialogClass: {
    type: String,
    default: "",
  },
});

// Emits 定义
const emit = defineEmits([
  "update:modelValue",
  "close",
  "open",
  "opened",
  "closed",
]);

// 响应式数据
const isFullscreen = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const computedWidth = computed(() => {
  if (isFullscreen.value) return "100%";
  return typeof props.width === "number" ? `${props.width}px` : props.width;
});

// 方法
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 处理弹框关闭事件（用户点击关闭按钮或按ESC键）
const handleClose = (done) => {
  // 触发 close 事件，让父组件决定是否关闭
  emit("close");

  // 使用 Element Plus 的标准方式：让 done() 回调处理关闭
  // 这样 Element Plus 会自动更新 v-model，避免双重更新导致的状态问题
  if (typeof done === "function") {
    done();
  } else {
    // 如果没有 done 回调（比如编程式调用），则手动更新状态
    emit("update:modelValue", false);
  }
};

// 处理弹框打开动画完成事件
const handleOpened = () => {
  emit("opened");
};

// 处理弹框关闭动画完成事件
const handleClosed = () => {
  emit("closed");
  // 在关闭动画完成后重置全屏状态
  isFullscreen.value = false;
};

// 监听弹框状态变化
watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    if (newVal && !oldVal) {
      // 弹框从关闭到打开
      emit("open");
    } else if (!newVal && oldVal) {
      // 弹框从打开到关闭
      // 不在这里重置全屏状态，而是在 handleClosed 中处理
    }
  },
  { immediate: false }
);

// 暴露方法和状态
defineExpose({
  isFullscreen,
  toggleFullscreen,
  handleClose,
});
</script>

<style scoped lang="scss">
.base-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    width: 100%;

    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      position: relative;
      top: -10px;

      .fullscreen-btn {
        padding: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;
        border: none;
        background: transparent;

        &:hover {
          background-color: #f0f8ff;
          color: #409eff;
        }

        &:focus {
          background-color: #f0f8ff;
          color: #409eff;
        }

        .el-icon {
          font-size: 16px;
        }
      }
    }
  }
}

// 全屏模式下的样式优化
:deep(.el-dialog.is-fullscreen) {
  .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 24px;
    height: calc(100vh - 120px);
    overflow-y: auto;
  }
}

// 非全屏模式下的样式优化
:deep(.el-dialog:not(.is-fullscreen)) {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
  }

  .el-dialog__body {
    padding: 20px;
  }
}

// 头部标题样式
:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 24px;
}

// 关闭按钮样式优化
:deep(.el-dialog__headerbtn) {
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;

  .el-dialog__close {
    font-size: 16px;
    color: #909399;

    &:hover {
      color: #409eff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog:not(.is-fullscreen)) {
    width: 95% !important;
    margin: 5vh auto;
  }
}
</style>
