---
type: "agent_requested"
description: "Example description"
---

代码命名规范
    - 变量命名：使用有意义的名称，避免使用单个字母或无意义的缩写。变量名应小写字母和下划线组合，如：user_id。
    - 函数命名：函数名应反映其功能，采用驼峰命名法,并使用小写字母开头。例如：getTotalCount。
    - 类命名：类名应采用驼峰命名法，并使用小写字母开头。例如：userModel。
    - 文件命名：文件名应与其中的内容相关，采用驼峰命名法，并使用小写字母开头。例如：userInfo.vue。

代码规范
    - 缩进：使用 4 个空格进行缩进，避免使用制表符。
    - 行长度：每行代码长度不超过 80 个字符。
    - 分号：在每个语句的末尾使用分号，即使该语句只有一行。
    - 行文风格：遵循统一的行文风格，避免出现中英文混合的情况。
    - 空格：在运算符周围使用空格，例如 a = b + c。
    - 注释：对复杂代码或逻辑进行注释，以便于他人理解。注释应简洁明了，避免过多的解释性注释。
    - 函数长度：单个函数或方法长度不宜过长，应尽量将功能单一的函数或方法拆分成多个小函数或方法。
    - 模块化：将功能相关的代码组织成模块或组件，提高代码的可重用性和可维护性。
    - 错误处理：对可能出现的错误进行捕获和处理，避免程序崩溃或异常行为。
    - 多态性：尽量利用函数重载和虚函数实现多态性，提高代码的复用率。
    - 性能优化：在保证功能正确的前提下，尽可能提高代码的性能和响应速度。
    - 兼容性：编写兼容性强的代码，确保在多种浏览器和设备上都能正常运行。
    - 日志输出：在关键位置输出日志，便于排查问题和调试程序。

响应式与状态管理
    - 使用 ref 和 reactive 进行响应式数据的定义，确保数据的双向绑定和自动更新。
    - 避免在模板中直接修改响应式数据，使用计算属性或方法进行修改。
    - 利用 Pinia 进行状态管理，将状态和相关操作封装在 store 中，使用 defineStore 创建 store。

代码组织与可维护性
    - 将大型组件拆分成更小、更可维护的子组件，利用插槽（slots）来实现更灵活的组件组合。
    - 提取可复用的逻辑部分，将其封装成函数或自定义 hooks，以便在不同组件中共享。
    - 对于复杂逻辑或关键算法，添加清晰的注释，解释代码的用途、实现细节和可能的边界情况。
    - 为重要的组件、模块和公共方法编写文档，包括使用示例、参数说明和返回值等。

组件通信
    - 使用 @emit 或自定义事件进行父子组件通信，确保组件解耦。
    - 采用事件总线或其他状态管理工具进行兄弟组件通信。
    - 避免在模板中直接编写复杂的逻辑，将逻辑封装为方法或计算属性。
    - 统一事件处理函数的命名规范，使用清晰且语义化的命名方式。
    - 使用对象或事件对象来传递多个参数，提高代码的可读性。
 
 性能优化
    - 对于大型应用，考虑采用懒加载（lazy loading）和异步加载来减少初始加载时间。
    - 在处理大量数据时，考虑使用虚拟列表来提高列表性能，只渲染当前可见区域的数据。